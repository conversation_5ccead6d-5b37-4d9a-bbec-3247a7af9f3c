part of pose_landmarker_plugin;

/// Represents the result of pose detection for a single person
class PoseDetectionResult {
  /// List of 33 pose landmarks
  final List<PoseLandmark> landmarks;
  
  /// Optional confidence score for the overall pose detection
  final double? confidence;

  const PoseDetectionResult({
    required this.landmarks,
    this.confidence,
  });

  factory PoseDetectionResult.fromMap(Map<String, dynamic> map) {
    final landmarksList = map['landmarks'] as List<dynamic>?;
    final landmarks = landmarksList
        ?.map((landmark) => PoseLandmark.fromMap(landmark as Map<String, dynamic>))
        .toList() ?? [];

    return PoseDetectionResult(
      landmarks: landmarks,
      confidence: (map['confidence'] as num?)?.toDouble(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'landmarks': landmarks.map((landmark) => landmark.toMap()).toList(),
      'confidence': confidence,
    };
  }

  /// Get a specific landmark by type
  PoseLandmark? getLandmark(PoseLandmarkType type) {
    if (type.index < landmarks.length) {
      return landmarks[type.index];
    }
    return null;
  }

  /// Check if the pose detection result is valid (has all 33 landmarks)
  bool get isValid => landmarks.length == 33;

  /// Get the center point of the pose (average of all landmarks)
  PoseLandmark? get center {
    if (landmarks.isEmpty) return null;
    
    double avgX = 0, avgY = 0, avgZ = 0;
    for (final landmark in landmarks) {
      avgX += landmark.x;
      avgY += landmark.y;
      avgZ += landmark.z;
    }
    
    final count = landmarks.length;
    return PoseLandmark(
      x: avgX / count,
      y: avgY / count,
      z: avgZ / count,
      visibility: 1.0,
      presence: 1.0,
    );
  }

  /// Get bounding box of the pose
  Map<String, double>? get boundingBox {
    if (landmarks.isEmpty) return null;
    
    double minX = landmarks.first.x;
    double maxX = landmarks.first.x;
    double minY = landmarks.first.y;
    double maxY = landmarks.first.y;
    
    for (final landmark in landmarks) {
      minX = math.min(minX, landmark.x);
      maxX = math.max(maxX, landmark.x);
      minY = math.min(minY, landmark.y);
      maxY = math.max(maxY, landmark.y);
    }
    
    return {
      'left': minX,
      'top': minY,
      'right': maxX,
      'bottom': maxY,
      'width': maxX - minX,
      'height': maxY - minY,
    };
  }

  /// Calculate the average visibility score
  double get averageVisibility {
    if (landmarks.isEmpty) return 0.0;
    
    double totalVisibility = 0;
    for (final landmark in landmarks) {
      totalVisibility += landmark.visibility;
    }
    
    return totalVisibility / landmarks.length;
  }

  /// Calculate the average presence score
  double get averagePresence {
    if (landmarks.isEmpty) return 0.0;
    
    double totalPresence = 0;
    for (final landmark in landmarks) {
      totalPresence += landmark.presence;
    }
    
    return totalPresence / landmarks.length;
  }

  @override
  String toString() {
    return 'PoseDetectionResult(landmarks: ${landmarks.length}, confidence: $confidence, avgVisibility: ${averageVisibility.toStringAsFixed(2)})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PoseDetectionResult &&
        other.landmarks.length == landmarks.length &&
        other.confidence == confidence;
  }

  @override
  int get hashCode => landmarks.hashCode ^ confidence.hashCode;
}

// Import math for min/max functions
import 'dart:math' as math;