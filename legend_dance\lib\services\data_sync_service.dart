// package:keepdance/services/data_sync_service.dart

import 'dart:async';
import 'package:get/get.dart';
import 'package:logger/logger.dart';

// 假设这些服务类存在于项目的其他地方
import 'package:keepdance/services/local_database_service.dart';
import 'package:keepdance/pages/creation/services/my_works_service.dart';

/// 数据同步服务，负责本地数据与源数据的同步。
/// 继承自 GetxService，由 GetX 管理其生命周期。
class DataSyncService extends GetxService {
  // 静态日志记录器，用于输出服务运行信息。
  static late final Logger _logger = Logger();

  // 依赖的服务，使用 'late final' 延迟初始化。
  late final LocalDatabaseService _localDatabaseService;
  late final MyWorksService _myWorksService;

  // -- 响应式状态字段 (Rx) --
  // 使用 .obs 创建响应式变量，用于UI更新。

  /// 是否正在同步中。
  final isSyncing = false.obs;

  /// 当前同步状态的文本描述。
  final syncStatus = ''.obs;

  /// 同步进度（0-100）。
  final syncProgress = 0.obs;

  /// 需要同步的总项目数。
  final totalToSync = 0.obs;

  /// 当前同步模式（例如 "full"）。
  final syncMode = ''.obs;

  // -- 内部状态字段 --

  /// 上次同步完成的时间。
  DateTime? _lastSyncTime;

  /// 当前的重试次数。
  int _retryCount = 0;

  /// 服务初始化时的回调。
  @override
  Future<void> onInit() async {
    super.onInit();
    await _initializeDependencies();
  }

  /// 初始化所需的服务依赖。
  Future<void> _initializeDependencies() async {
    // 查找 MyWorksService，应该已经在main.dart中注册
    _myWorksService = Get.find<MyWorksService>();

    // 确保 LocalDatabaseService 已注册。
    if (!Get.isRegistered<LocalDatabaseService>()) {
      Get.put(LocalDatabaseService(), permanent: true);
    }
    _localDatabaseService = Get.find<LocalDatabaseService>();
  }

  /// 执行完整的数据同步。
  ///
  /// [isRetry] 是一个内部参数，用于标记本次调用是否为错误重试。
  /// 如果是重试，将跳过清理本地缓存的步骤。
  Future<bool> performFullSync({bool isRetry = false}) async {
    // 如果已经在同步，则直接返回 false，避免并发执行。
    if (isSyncing.value) {
      return false;
    }

    // 优化：如果不是重试，且距离上次同步时间很短（<2秒），则检查是否需要同步。
    if (!isRetry &&
        _lastSyncTime != null &&
        DateTime.now().difference(_lastSyncTime!).inSeconds < 2) {
      try {
        final stats = await _localDatabaseService.getDatabaseStats();
        // 如果数据库中已有数据，则暂时不同步，避免过于频繁。
        if (stats['totalWorks'] != null && (stats['totalWorks'] as int) > 0) {
          return false;
        }
      } catch (e) {
        // 检查出错时，忽略错误并继续执行同步。
      }
    }

    try {
      // -- 1. 准备同步状态 --
      _lastSyncTime = DateTime.now();
      isSyncing.value = true;
      syncMode.value = 'full';
      syncStatus.value = '开始数据同步';
      syncProgress.value = 0;

      _logger.i("开始完整数据同步${isRetry ? '（强制模式）' : ''}");

      // 如果不是重试，则清空本地数据库。
      if (!isRetry) {
        syncStatus.value = '清空本地缓存';
        await _localDatabaseService.clearAllWorkItems();
      }

      // -- 2. 加载待同步数据 --
      syncStatus.value = '加载作品数据';
      final worksToSync = await _myWorksService.loadLocalWorks();
      totalToSync.value = worksToSync.length;

      if (worksToSync.isEmpty) {
        syncStatus.value = '没有数据需要同步';
        _logger.i('没有需要同步的作品。');
        return true; // 同步成功，因为没有任务。
      }

      _logger.i("开始同步 ${worksToSync.length} 个作品");

      // -- 3. 分批执行同步 --
      int savedCount = 0;
      const batchSize = 50; // 根据汇编分析，批处理大小为50。

      for (int i = 0; i < worksToSync.length; i += batchSize) {
        final endIndex = (i + batchSize).clamp(0, worksToSync.length);
        final batch = worksToSync.sublist(i, endIndex);

        syncStatus.value = "同步数据 ${i + 1}-${endIndex}/${worksToSync.length}";

        final int savedInBatch = await _localDatabaseService.saveWorkItemsBatch(
          batch,
        );
        savedCount += savedInBatch;

        syncProgress.value = ((savedCount / worksToSync.length) * 100).round();

        // 短暂延迟，以允许UI刷新并避免过度占用CPU/DB。
        await Future.delayed(const Duration(milliseconds: 100));
      }

      // -- 4. 同步完成 --
      syncStatus.value = '同步完成';
      _logger.i("完整同步完成: $savedCount/${worksToSync.length} 个作品");

      _retryCount = 0; // 成功后重置重试计数。
      return savedCount == worksToSync.length;
    } catch (error, stackTrace) {
      // -- 错误处理与重试 --
      // 创建一个闭包，用于在需要时重新调用此函数进行重试。
      return await _handleSyncError(
        error,
        stackTrace,
        () => performFullSync(isRetry: true),
      );
    } finally {
      // -- 5. 确保最终重置状态 --
      // 检查服务是否仍然被注册，防止在服务已销毁后访问状态。
      if (Get.isRegistered<DataSyncService>()) {
        isSyncing.value = false;
        syncMode.value = '';
      }
    }
  }

  /// 处理同步过程中发生的错误，并根据错误类型决定是否重试。
  Future<bool> _handleSyncError(
    dynamic error,
    StackTrace stackTrace,
    Future<bool> Function() retryAction,
  ) async {
    _logger.e("完整同步失败: $error");

    final errorString = error.toString().toLowerCase();
    final bool isNetworkError =
        errorString.contains('network') ||
        errorString.contains('timeout') ||
        errorString.contains('connection');
    final bool isDatabaseError =
        errorString.contains('database') || errorString.contains('sql');

    // 如果是网络或数据库错误，且重试次数小于3，则进行重试。
    const maxRetries = 3;
    final bool shouldRetry =
        (isNetworkError || isDatabaseError) && _retryCount < maxRetries;

    if (shouldRetry) {
      _retryCount++;
      syncStatus.value = "完整同步失败，正在重试 ($_retryCount/$maxRetries)";
      String errorType = isNetworkError
          ? '网络'
          : (isDatabaseError ? '数据库' : '未知');
      _logger.w("完整同步失败，开始第$_retryCount次重试，错误类型: $errorType");

      // 等待3秒后重试。
      await Future.delayed(const Duration(seconds: 3));

      // 允许重试操作重新设置同步状态。
      isSyncing.value = false;
      syncMode.value = '';

      return await retryAction();
    } else {
      // 如果不重试，则彻底失败并重置所有状态。
      _retryCount = 0;
      syncStatus.value = "完整同步失败: $error";
      _logger.e(syncStatus.value);
      resetSyncState();
      return false;
    }
  }

  /// 将所有同步相关的状态重置为初始值。
  void resetSyncState() {
    isSyncing.value = false;
    syncMode.value = '';
    syncStatus.value = '已重置';
    syncProgress.value = 0;
    totalToSync.value = 0;
    _lastSyncTime = null;
    _retryCount = 0;
  }

  /// 同步（保存）单个作品项。
  Future<bool> syncSingleWorkItem(dynamic workItem) async {
    try {
      return await _localDatabaseService.saveWorkItem(workItem);
    } catch (error) {
      _logger.e("同步单个作品失败: $error");
      return false;
    }
  }

  /// 同步删除一个作品项。
  Future<bool> syncDeleteWorkItem(dynamic workItemId) async {
    try {
      return await _localDatabaseService.deleteWorkItem(workItemId);
    } catch (error) {
      _logger.e("同步删除作品失败: $error");
      return false;
    }
  }

  /// 检查源数据和本地数据库之间的数据一致性。
  Future<Map<String, dynamic>> checkDataConsistency() async {
    try {
      final sourceWorks = await _myWorksService.loadLocalWorks();
      final stats = await _localDatabaseService.getDatabaseStats();

      final int sourceCount = sourceWorks.length;
      final int databaseCount = (stats['totalWorks'] as int?) ?? 0;

      final int countDifference = (sourceCount - databaseCount).abs();

      // 计算一致性比率，处理除零情况。
      final double consistencyRatio = (sourceCount == 0)
          ? (databaseCount == 0 ? 1.0 : 0.0)
          : (sourceCount - countDifference) / sourceCount;

      final bool isConsistent = countDifference == 0;
      // 当数据不一致或一致性低于95%时，需要同步。
      final bool needsSync = !isConsistent || consistencyRatio < 0.95;

      final result = {
        'sourceCount': sourceCount,
        'databaseCount': databaseCount,
        'countDifference': countDifference,
        'consistencyRatio': consistencyRatio,
        'isConsistent': isConsistent,
        'needsSync': needsSync,
      };

      // 如果数据不一致，记录日志。
      if (!isConsistent) {
        _logger.i("数据一致性检查结果: $result");
      }

      return result;
    } catch (error) {
      _logger.e("数据一致性检查失败: $error");
      // 返回一个表示错误的默认结果。
      return {
        'sourceCount': 0,
        'databaseCount': 0,
        'countDifference': 0,
        'consistencyRatio': 0.0,
        'isConsistent': false,
        'needsSync': true,
        'error': error.toString(),
      };
    }
  }
}
