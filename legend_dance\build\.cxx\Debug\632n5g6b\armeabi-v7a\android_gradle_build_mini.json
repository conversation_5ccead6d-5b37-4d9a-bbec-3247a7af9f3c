{"buildFiles": ["D:\\Program Files\\Flutterdev\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\project\\ai-dance\\legend_dance\\build\\.cxx\\Debug\\632n5g6b\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\project\\ai-dance\\legend_dance\\build\\.cxx\\Debug\\632n5g6b\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}