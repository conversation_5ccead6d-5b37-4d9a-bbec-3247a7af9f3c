1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.legend_dance"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->D:\project\ai-dance\legend_dance\android\app\src\debug\AndroidManifest.xml:6:5-66
15-->D:\project\ai-dance\legend_dance\android\app\src\debug\AndroidManifest.xml:6:22-64
16    <!--
17     Required to query activities that can process text, see:
18         https://developer.android.com/training/package-visibility and
19         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
20
21         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
22    -->
23    <queries>
23-->D:\project\ai-dance\legend_dance\android\app\src\main\AndroidManifest.xml:39:5-44:15
24        <intent>
24-->D:\project\ai-dance\legend_dance\android\app\src\main\AndroidManifest.xml:40:9-43:18
25            <action android:name="android.intent.action.PROCESS_TEXT" />
25-->D:\project\ai-dance\legend_dance\android\app\src\main\AndroidManifest.xml:41:13-72
25-->D:\project\ai-dance\legend_dance\android\app\src\main\AndroidManifest.xml:41:21-70
26
27            <data android:mimeType="text/plain" />
27-->D:\project\ai-dance\legend_dance\android\app\src\main\AndroidManifest.xml:42:13-50
27-->D:\project\ai-dance\legend_dance\android\app\src\main\AndroidManifest.xml:42:19-48
28        </intent>
29
30        <package android:name="com.tencent.mm" />
30-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-50
30-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:18-47
31    </queries>
32
33    <uses-feature android:name="android.hardware.camera.any" />
33-->[:camera_android_camerax] D:\project\ai-dance\legend_dance\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-64
33-->[:camera_android_camerax] D:\project\ai-dance\legend_dance\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:19-61
34
35    <uses-permission android:name="android.permission.CAMERA" />
35-->[:camera_android_camerax] D:\project\ai-dance\legend_dance\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-65
35-->[:camera_android_camerax] D:\project\ai-dance\legend_dance\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-62
36    <uses-permission android:name="android.permission.RECORD_AUDIO" />
36-->[:camera_android_camerax] D:\project\ai-dance\legend_dance\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-71
36-->[:camera_android_camerax] D:\project\ai-dance\legend_dance\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:22-68
37    <uses-permission
37-->[:camera_android_camerax] D:\project\ai-dance\legend_dance\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-13:38
38        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
38-->[:camera_android_camerax] D:\project\ai-dance\legend_dance\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-65
39        android:maxSdkVersion="28" />
39-->[:camera_android_camerax] D:\project\ai-dance\legend_dance\build\camera_android_camerax\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-35
40    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
41    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
41-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-79
41-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-76
42    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
42-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-76
42-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-73
43
44    <permission
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
45        android:name="com.example.legend_dance.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
46        android:protectionLevel="signature" />
46-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
47
48    <uses-permission android:name="com.example.legend_dance.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
48-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
49
50    <application
51        android:name="android.app.Application"
52        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\73aa083afefb941d18007d1b70cec6be\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
53        android:debuggable="true"
54        android:extractNativeLibs="false"
55        android:icon="@mipmap/ic_launcher"
56        android:label="legend_dance" >
57        <activity
58            android:name="com.example.legend_dance.MainActivity"
59            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
60            android:exported="true"
61            android:hardwareAccelerated="true"
62            android:launchMode="singleTop"
63            android:taskAffinity=""
64            android:theme="@style/LaunchTheme"
65            android:windowSoftInputMode="adjustResize" >
66
67            <!--
68                 Specifies an Android theme to apply to this Activity as soon as
69                 the Android process has started. This theme is visible to the user
70                 while the Flutter UI initializes. After that, this theme continues
71                 to determine the Window background behind the Flutter UI.
72            -->
73            <meta-data
74                android:name="io.flutter.embedding.android.NormalTheme"
75                android:resource="@style/NormalTheme" />
76
77            <intent-filter>
78                <action android:name="android.intent.action.MAIN" />
79
80                <category android:name="android.intent.category.LAUNCHER" />
81            </intent-filter>
82        </activity>
83        <!--
84             Don't delete the meta-data below.
85             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
86        -->
87        <meta-data
88            android:name="flutterEmbedding"
89            android:value="2" />
90
91        <activity
91-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:9-22:58
92            android:name="com.jarvan.fluwx.wxapi.FluwxWXEntryActivity"
92-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-71
93            android:exported="false"
93-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:13-37
94            android:launchMode="singleTask"
94-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:13-44
95            android:taskAffinity="com.example.legend_dance"
95-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:21:13-52
96            android:theme="@style/DisablePreviewTheme" />
96-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:22:13-55
97
98        <activity-alias
98-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:9-36:58
99            android:name="com.example.legend_dance.wxapi.WXEntryActivity"
99-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:31:13-66
100            android:exported="true"
100-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:32:13-36
101            android:launchMode="singleTop"
101-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:33:13-43
102            android:targetActivity="com.jarvan.fluwx.wxapi.FluwxWXEntryActivity"
102-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:34:13-81
103            android:taskAffinity="com.example.legend_dance"
103-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:35:13-52
104            android:theme="@style/DisablePreviewTheme" />
104-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:36:13-55
105        <activity-alias
105-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:9-29:58
106            android:name="com.example.legend_dance.wxapi.WXPayEntryActivity"
106-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-69
107            android:exported="true"
107-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-36
108            android:launchMode="singleInstance"
108-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-48
109            android:targetActivity="com.jarvan.fluwx.wxapi.FluwxWXEntryActivity"
109-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-81
110            android:theme="@style/DisablePreviewTheme" />
110-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-55
111
112        <provider
112-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:38:9-46:20
113            android:name="com.jarvan.fluwx.FluwxFileProvider"
113-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:39:13-62
114            android:authorities="com.example.legend_dance.fluwxprovider"
114-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:40:13-65
115            android:exported="false"
115-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:41:13-37
116            android:grantUriPermissions="true" >
116-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:42:13-47
117            <meta-data
117-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-45:69
118                android:name="android.support.FILE_PROVIDER_PATHS"
118-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:17-67
119                android:resource="@xml/fluwx_file_provider_paths" />
119-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:17-66
120        </provider>
121        <!--
122           Declares a provider which allows us to store files to share in
123           '.../caches/share_plus' and grant the receiving action access
124        -->
125        <provider
125-->[:share_plus] D:\project\ai-dance\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:20
126            android:name="dev.fluttercommunity.plus.share.ShareFileProvider"
126-->[:share_plus] D:\project\ai-dance\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-77
127            android:authorities="com.example.legend_dance.flutter.share_provider"
127-->[:share_plus] D:\project\ai-dance\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-74
128            android:exported="false"
128-->[:share_plus] D:\project\ai-dance\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
129            android:grantUriPermissions="true" >
129-->[:share_plus] D:\project\ai-dance\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-47
130            <meta-data
130-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:43:13-45:69
131                android:name="android.support.FILE_PROVIDER_PATHS"
131-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:44:17-67
132                android:resource="@xml/flutter_share_file_paths" />
132-->[:fluwx] D:\project\ai-dance\legend_dance\build\fluwx\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:45:17-66
133        </provider>
134        <!--
135           This manifest declared broadcast receiver allows us to use an explicit
136           Intent when creating a PendingItent to be informed of the user's choice
137        -->
138        <receiver
138-->[:share_plus] D:\project\ai-dance\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:9-32:20
139            android:name="dev.fluttercommunity.plus.share.SharePlusPendingIntent"
139-->[:share_plus] D:\project\ai-dance\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-82
140            android:exported="false" >
140-->[:share_plus] D:\project\ai-dance\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:28:13-37
141            <intent-filter>
141-->[:share_plus] D:\project\ai-dance\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:29:13-31:29
142                <action android:name="EXTRA_CHOSEN_COMPONENT" />
142-->[:share_plus] D:\project\ai-dance\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:17-65
142-->[:share_plus] D:\project\ai-dance\legend_dance\build\share_plus\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:30:25-62
143            </intent-filter>
144        </receiver>
145
146        <service
146-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\70734f771fc2ccac2c3fef0f568857c6\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:24:9-33:19
147            android:name="androidx.camera.core.impl.MetadataHolderService"
147-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\70734f771fc2ccac2c3fef0f568857c6\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:25:13-75
148            android:enabled="false"
148-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\70734f771fc2ccac2c3fef0f568857c6\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:26:13-36
149            android:exported="false" >
149-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\70734f771fc2ccac2c3fef0f568857c6\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:27:13-37
150            <meta-data
150-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\70734f771fc2ccac2c3fef0f568857c6\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:30:13-32:89
151                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
151-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\70734f771fc2ccac2c3fef0f568857c6\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:31:17-103
152                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
152-->[androidx.camera:camera-camera2:1.5.0-beta01] C:\Users\<USER>\.gradle\caches\8.12\transforms\70734f771fc2ccac2c3fef0f568857c6\transformed\jetified-camera-camera2-1.5.0-beta01\AndroidManifest.xml:32:17-86
153        </service>
154
155        <uses-library
155-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
156            android:name="androidx.window.extensions"
156-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
157            android:required="false" />
157-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
158        <uses-library
158-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
159            android:name="androidx.window.sidecar"
159-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
160            android:required="false" />
160-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\26c99be856553367d8fad52c95155b00\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
161
162        <provider
162-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c54ac46263f2b3b33f56312459bb23ee\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
163            android:name="androidx.startup.InitializationProvider"
163-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c54ac46263f2b3b33f56312459bb23ee\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:25:13-67
164            android:authorities="com.example.legend_dance.androidx-startup"
164-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c54ac46263f2b3b33f56312459bb23ee\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:26:13-68
165            android:exported="false" >
165-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c54ac46263f2b3b33f56312459bb23ee\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:27:13-37
166            <meta-data
166-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c54ac46263f2b3b33f56312459bb23ee\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
167                android:name="androidx.emoji2.text.EmojiCompatInitializer"
167-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c54ac46263f2b3b33f56312459bb23ee\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:30:17-75
168                android:value="androidx.startup" />
168-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c54ac46263f2b3b33f56312459bb23ee\transformed\jetified-emoji2-1.2.0\AndroidManifest.xml:31:17-49
169            <meta-data
169-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
170                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
170-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
171                android:value="androidx.startup" />
171-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\c26e9bd9e94c83e6b65abe3b819ef578\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
172            <meta-data
172-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
173                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
173-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
174                android:value="androidx.startup" />
174-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
175        </provider>
176
177        <receiver
177-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
178            android:name="androidx.profileinstaller.ProfileInstallReceiver"
178-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
179            android:directBootAware="false"
179-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
180            android:enabled="true"
180-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
181            android:exported="true"
181-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
182            android:permission="android.permission.DUMP" >
182-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
183            <intent-filter>
183-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
184                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
184-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
184-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
185            </intent-filter>
186            <intent-filter>
186-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
187                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
187-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
187-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
188            </intent-filter>
189            <intent-filter>
189-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
190                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
190-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
190-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
191            </intent-filter>
192            <intent-filter>
192-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
193                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
193-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
193-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\85792c7efa6a1c8df0b33488ef129b4a\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
194            </intent-filter>
195        </receiver>
196
197        <service
197-->[com.google.android.datatransport:transport-backend-cct:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70728dc7fd66d07706387bf9e9879d14\transformed\jetified-transport-backend-cct-3.1.0\AndroidManifest.xml:29:9-35:19
198            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
198-->[com.google.android.datatransport:transport-backend-cct:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70728dc7fd66d07706387bf9e9879d14\transformed\jetified-transport-backend-cct-3.1.0\AndroidManifest.xml:30:13-103
199            android:exported="false" >
199-->[com.google.android.datatransport:transport-backend-cct:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70728dc7fd66d07706387bf9e9879d14\transformed\jetified-transport-backend-cct-3.1.0\AndroidManifest.xml:31:13-37
200            <meta-data
200-->[com.google.android.datatransport:transport-backend-cct:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70728dc7fd66d07706387bf9e9879d14\transformed\jetified-transport-backend-cct-3.1.0\AndroidManifest.xml:32:13-34:39
201                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
201-->[com.google.android.datatransport:transport-backend-cct:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70728dc7fd66d07706387bf9e9879d14\transformed\jetified-transport-backend-cct-3.1.0\AndroidManifest.xml:33:17-94
202                android:value="cct" />
202-->[com.google.android.datatransport:transport-backend-cct:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\70728dc7fd66d07706387bf9e9879d14\transformed\jetified-transport-backend-cct-3.1.0\AndroidManifest.xml:34:17-36
203        </service>
204        <service
204-->[com.google.android.datatransport:transport-runtime:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0fe884ed5ede762e615329269af09ff3\transformed\jetified-transport-runtime-3.1.0\AndroidManifest.xml:26:9-30:19
205            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
205-->[com.google.android.datatransport:transport-runtime:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0fe884ed5ede762e615329269af09ff3\transformed\jetified-transport-runtime-3.1.0\AndroidManifest.xml:27:13-117
206            android:exported="false"
206-->[com.google.android.datatransport:transport-runtime:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0fe884ed5ede762e615329269af09ff3\transformed\jetified-transport-runtime-3.1.0\AndroidManifest.xml:28:13-37
207            android:permission="android.permission.BIND_JOB_SERVICE" >
207-->[com.google.android.datatransport:transport-runtime:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0fe884ed5ede762e615329269af09ff3\transformed\jetified-transport-runtime-3.1.0\AndroidManifest.xml:29:13-69
208        </service>
209
210        <receiver
210-->[com.google.android.datatransport:transport-runtime:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0fe884ed5ede762e615329269af09ff3\transformed\jetified-transport-runtime-3.1.0\AndroidManifest.xml:32:9-34:40
211            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
211-->[com.google.android.datatransport:transport-runtime:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0fe884ed5ede762e615329269af09ff3\transformed\jetified-transport-runtime-3.1.0\AndroidManifest.xml:33:13-132
212            android:exported="false" />
212-->[com.google.android.datatransport:transport-runtime:3.1.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0fe884ed5ede762e615329269af09ff3\transformed\jetified-transport-runtime-3.1.0\AndroidManifest.xml:34:13-37
213    </application>
214
215</manifest>
