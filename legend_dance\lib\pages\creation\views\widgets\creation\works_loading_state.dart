// lib: , url: package:keepdance/pages/creation/views/widgets/creation/works_loading_state.dart

import 'dart:math';

// 推测路径
import 'package:flutter/material.dart';
// 推测路径
import 'package:flutter_screenutil/flutter_screenutil.dart';
// 推测路径
import 'package:get/get.dart';
// 推测路径
import 'package:keepdance/app_theme/app_theme.dart';
// 推测路径
import 'package:keepdance/pages/creation/controllers/creation_controller.dart';
// 推测路径
import 'package:keepdance/models/work_item_data.dart';

///
/// 作品加载状态视图
///
class WorksLoadingState extends StatefulWidget {
  const WorksLoadingState({Key? key}) : super(key: key);

  @override
  State<WorksLoadingState> createState() => _WorksLoadingStateState();
}

class _WorksLoadingStateState extends State<WorksLoadingState>
    with TickerProviderStateMixin {
  late final AnimationController _rotationController;
  late final AnimationController _pulseController;
  late final Animation<double> _rotationAnimation;
  late final Animation<double> _pulseAnimation;

  // 延迟获取CreationController，避免在构造时出错
  CreationController? _creationController;

  /// 安全获取CreationController
  CreationController? get creationController {
    _creationController ??= Get.isRegistered<CreationController>()
        ? Get.find<CreationController>()
        : null;
    return _creationController;
  }

  @override
  void initState() {
    super.initState();
    _rotationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _rotationAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _rotationController, curve: Curves.linear),
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _rotationController.repeat();
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  /// 计算总体进度
  double _calculateOverallProgress() {
    final controller = creationController;
    if (controller == null) return 0.5; // 默认进度

    double progress = controller.isScanning.value ? 0.2 : 0.0;

    if (controller.totalCount.value > 0) {
      final processedRatio =
          controller.processedCount.value / controller.totalCount.value;
      progress += processedRatio * 0.6;
    } else {
      progress += 0.3;
    }

    if (controller.isCategorizing.value) {
      if (controller.processedCount.value > 0) {
        progress += 0.2;
      }
    } else {
      // 检查完成状态
      final bool isDone = controller.isDone.value;
      if (isDone) {
        return 1.0;
      }
    }

    return progress.clamp(0.0, 1.0);
  }

  /// 获取进度文本
  String _getProgressText() {
    final controller = creationController;
    if (controller != null &&
        controller.totalCount.value > 0 &&
        controller.processedCount.value > 0) {
      return '${controller.processedCount.value}/${controller.totalCount.value} 个作品';
    }
    return '正在处理数据';
  }

  /// 获取当前加载阶段信息
  Map<String, String> _getCurrentLoadingStage() {
    double progress = _calculateOverallProgress();
    if (progress < 0.3) {
      return {'title': '正在扫描本地作品', 'subtitle': '检测您的舞蹈视频文件...'};
    } else if (progress < 0.6) {
      return {'title': '正在加载作品信息', 'subtitle': '读取视频元数据和封面...'};
    } else if (progress < 0.9) {
      return {'title': '正在整理分类数据', 'subtitle': '为您准备精彩的舞蹈内容...'};
    } else {
      return {'title': '即将完成', 'subtitle': '马上就可以开始您的舞蹈之旅！'};
    }
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(64.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            AnimatedBuilder(
              animation: Listenable.merge([
                _rotationAnimation,
                _pulseAnimation,
              ]),
              builder: (context, child) {
                return Transform.scale(
                  scale: _pulseAnimation.value,
                  child: Transform(
                    transform: Matrix4.rotationZ(
                      _rotationAnimation.value * 2 * pi,
                    ),
                    alignment: Alignment.center,
                    child: Container(
                      width: 200.w,
                      height: 200.w,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: LinearGradient(
                          colors: [
                            const Color(0xFFFFFFFF).withOpacity(0.2),
                            const Color(0xFF00C2FF).withOpacity(0.1),
                            const Color(0xFFFFFFFF).withOpacity(0.15),
                          ],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: const Color(0xFFFFFFFF).withOpacity(0.2),
                            blurRadius: 25.0,
                            spreadRadius: 2.0,
                            offset: const Offset(10, 10),
                          ),
                          BoxShadow(
                            color: const Color(0xFFFFFFFF).withOpacity(0.1),
                            blurRadius: 40.0,
                            spreadRadius: 4.0,
                            offset: const Offset(-10, -10),
                          ),
                        ],
                      ),
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          Obx(() {
                            double progress = _calculateOverallProgress();
                            return SizedBox(
                              width: 160.w,
                              height: 160.w,
                              child: CircularProgressIndicator(
                                value: progress > 0 ? progress : null,
                                backgroundColor: const Color(
                                  0xfff0f0f0,
                                ).withOpacity(0.3),
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  const Color(0xFFFFFFFF).withOpacity(0.8),
                                ),
                                strokeWidth: 8.w,
                              ),
                            );
                          }),
                          Icon(
                            Icons.camera_alt,
                            size: 56.sp,
                            color: const Color(0xFFFFFFFF).withOpacity(0.9),
                            semanticLabel: 'Camera Icon', // 添加语义标签，好习惯
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
            SizedBox(height: 56.h),
            Obx(() {
              final stage = _getCurrentLoadingStage();
              final title = stage['title'] ?? '';
              final subtitle = stage['subtitle'] ?? '';

              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    title,
                    textAlign: TextAlign.center,
                    style: AppTheme.titleStyle.copyWith(
                      fontSize: 36.sp,
                      fontWeight: FontWeight.w600,
                      color: const Color(0xFFE0E0E0),
                    ),
                  ),
                  SizedBox(height: 16.h),
                  Text(
                    subtitle,
                    textAlign: TextAlign.center,
                    style: AppTheme.bodyStyle.copyWith(
                      fontSize: 28.sp,
                      height: 1.4,
                      color: const Color(0xFFBDBDBD),
                    ),
                  ),
                ],
              );
            }),
            SizedBox(height: 40.h),
            Obx(() {
              final progressText = _getProgressText();
              final overallProgress = _calculateOverallProgress();

              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    width: 480.w,
                    child: Container(
                      height: 12.h,
                      decoration: BoxDecoration(
                        color: const Color(0xFFE0E0E0).withOpacity(0.15),
                        borderRadius: BorderRadius.circular(6.h),
                        shape: BoxShape.rectangle,
                      ),
                      child: Stack(
                        children: [
                          FractionallySizedBox(
                            widthFactor: overallProgress,
                            alignment: Alignment.centerLeft,
                            child: Container(
                              decoration: BoxDecoration(
                                shape: BoxShape.rectangle,
                                borderRadius: BorderRadius.circular(6.h),
                                gradient: LinearGradient(
                                  colors: [
                                    Colors.blueAccent.withOpacity(0.1),
                                    Colors.blueAccent.withOpacity(0.05),
                                  ],
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight,
                                ),
                              ),
                            ),
                          ),
                          Container(
                            decoration: BoxDecoration(
                              shape: BoxShape.rectangle,
                              borderRadius: BorderRadius.circular(6.h),
                              gradient: LinearGradient(
                                colors: [
                                  Colors.white.withOpacity(0.8),
                                  Colors.white,
                                ],
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.white.withOpacity(0.4),
                                  blurRadius: 8.0,
                                  offset: const Offset(0.0, 0.0),
                                  spreadRadius: 0,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: 24.h),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(
                        progressText,
                        style: AppTheme.captionStyle.copyWith(
                          fontSize: 24.sp,
                          fontWeight: FontWeight.normal,
                          color: const Color(0xFFE0E0E0),
                        ),
                      ),
                      Text(
                        '${(overallProgress * 100).toInt()}%',
                        style: AppTheme.captionStyle.copyWith(
                          fontSize: 24.sp,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
                ],
              );
            }),
            SizedBox(height: 32.h),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 32.w, vertical: 24.h),
              decoration: BoxDecoration(
                color: const Color(0xFFFFFFFF).withOpacity(0.05),
                borderRadius: BorderRadius.circular(24.r),
                shape: BoxShape.rectangle,
                border: Border.all(
                  color: const Color(0xFFFFFFFF).withOpacity(0.1),
                  width: 1.0,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.info_outline,
                    size: 32.sp,
                    color: const Color(0xFFFFFFFF).withOpacity(0.7),
                  ),
                  SizedBox(width: 16.w),
                  Flexible(
                    child: Text(
                      '首次安装后需要初始化数据，请稍候...',
                      style: AppTheme.captionStyle.copyWith(
                        fontSize: 22.sp,
                        height: 1.3,
                        color: const Color(0xFFFFFFFF).withOpacity(0.8),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
