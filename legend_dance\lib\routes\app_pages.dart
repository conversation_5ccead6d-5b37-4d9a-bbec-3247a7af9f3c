// lib: , url: package:keepdance/routes/app_pages.dart

import 'package:get/get.dart';
import 'package:keepdance/pages/creation/views/creation_view.dart';

// 简化的路由配置，只包含必要的路由
abstract class AppPages {
  AppPages._();

  static const INITIAL = '/';
  static const LOGIN = '/login';
  static const MAIN = '/main';
  static const HOME = '/home';
  static const CREATION = '/creation';

  // 静态路由列表字段
  static late final List<GetPage<dynamic>> routes = _initRoutes();

  // 初始化路由列表的私有方法
  static List<GetPage<dynamic>> _initRoutes() {
    return <GetPage<dynamic>>[
      // 登录页面路由
      GetPage(
        name: '/login-page',
        page: () => const CreationView(), // 临时使用CreationView作为占位符
        transition: Transition.rightToLeft,
        transitionDuration: Get.defaultTransitionDuration,
      ),
      GetPage(
        name: '/login',
        page: () => const CreationView(), // 临时使用CreationView作为占位符
        transition: Transition.downToUp,
        transitionDuration: Get.defaultTransitionDuration,
      ),
      // 主页面路由
      GetPage(
        name: '/main',
        page: () => const CreationView(), // 临时使用CreationView作为占位符
      ),
      GetPage(
        name: '/home',
        page: () => const CreationView(), // 临时使用CreationView作为占位符
        preventDuplicates: true,
        transition: Transition.rightToLeft,
        transitionDuration: Get.defaultTransitionDuration,
      ),
      // 创作页面路由
      GetPage(
        name: '/creation',
        page: () => const CreationView(),
        preventDuplicates: true,
        transition: Transition.rightToLeft,
        transitionDuration: const Duration(milliseconds: 300),
      ),
    ];
  }
}
