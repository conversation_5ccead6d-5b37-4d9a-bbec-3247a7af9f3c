package com.jxhy.partr;

import android.app.Activity;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.ImageFormat;
import android.graphics.Rect;
import android.graphics.YuvImage;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.google.mediapipe.framework.image.BitmapImageBuilder;
import com.google.mediapipe.framework.image.MPImage;
import com.google.mediapipe.tasks.core.BaseOptions;
import com.google.mediapipe.tasks.core.Delegate;
import com.google.mediapipe.tasks.vision.core.RunningMode;
import com.google.mediapipe.tasks.vision.poselandmarker.PoseLandmarker;
import com.google.mediapipe.tasks.vision.poselandmarker.PoseLandmarkerResult;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import io.flutter.embedding.engine.plugins.FlutterPlugin;
import io.flutter.embedding.engine.plugins.activity.ActivityAware;
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding;
import io.flutter.plugin.common.MethodCall;
import io.flutter.plugin.common.MethodChannel;
import io.flutter.plugin.common.MethodChannel.MethodCallHandler;
import io.flutter.plugin.common.MethodChannel.Result;

public class PoseLandmarkerPlugin implements FlutterPlugin, MethodCallHandler, ActivityAware {
    public static final String CHANNEL_NAME = "pose_landmarker_plugin";
    public static final String TAG = "PoseLandmarkerPlugin";

    private MethodChannel channel;
    private Context context;
    private Activity activity;
    private PoseLandmarker poseLandmarker;
    private boolean isInitialized = false;

    @Override
    public void onAttachedToEngine(@NonNull FlutterPluginBinding flutterPluginBinding) {
        channel = new MethodChannel(flutterPluginBinding.getBinaryMessenger(), CHANNEL_NAME);
        channel.setMethodCallHandler(this);
        context = flutterPluginBinding.getApplicationContext();
        Log.d(TAG, "PoseLandmarkerPlugin attached to engine");
    }

    @Override
    public void onMethodCall(@NonNull MethodCall call, @NonNull Result result) {
        Log.d(TAG, "Method called: " + call.method);
        
        switch (call.method) {
            case "initialize":
                initialize(call, result);
                break;
            case "detect":
                detect(call, result);
                break;
            case "detectRGBA":
                detectRGBA(call, result);
                break;
            case "resetInitializationState":
                resetInitializationState(result);
                break;
            case "release":
                release(result);
                break;
            default:
                result.notImplemented();
        }
    }

    private void initialize(MethodCall call, Result result) {
        try {
            Integer maxPosesArg = call.argument("maxPoses");
            int maxPoses = maxPosesArg != null ? maxPosesArg : 1;
            
            Boolean forceReinitializeArg = call.argument("forceReinitialize");
            boolean forceReinitialize = forceReinitializeArg != null ? forceReinitializeArg : false;

            if (isInitialized && !forceReinitialize) {
                Log.d(TAG, "PoseLandmarker already initialized");
                result.success(null);
                return;
            }

            // Release existing instance if reinitializing
            if (poseLandmarker != null) {
                poseLandmarker.close();
                poseLandmarker = null;
            }

            // Copy model file to cache
            String modelPath = copyAssetToCache("pose_landmarker_lite.task");
            if (modelPath == null) {
                result.error("MODEL_ERROR", "Failed to copy model file to cache", null);
                return;
            }

            // Create PoseLandmarker options
            PoseLandmarker.PoseLandmarkerOptions.Builder optionsBuilder = 
                PoseLandmarker.PoseLandmarkerOptions.builder()
                    .setBaseOptions(BaseOptions.builder()
                        .setDelegate(Delegate.GPU)
                        .setModelAssetPath(modelPath)
                        .build())
                    .setRunningMode(RunningMode.LIVE_STREAM)
                    .setNumPoses(maxPoses)
                    .setResultListener(this::handlePoseResults)
                    .setErrorListener(this::handleError);

            // Create PoseLandmarker
            poseLandmarker = PoseLandmarker.createFromOptions(context, optionsBuilder.build());
            isInitialized = true;
            
            Log.d(TAG, "PoseLandmarker initialized successfully with maxPoses: " + maxPoses);
            result.success(null);
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize PoseLandmarker", e);
            result.error("INIT_ERROR", "Failed to initialize: " + e.getMessage(), null);
        }
    }

    private void detect(MethodCall call, Result result) {
        if (!isInitialized || poseLandmarker == null) {
            result.error("NOT_INITIALIZED", "PoseLandmarker not initialized", null);
            return;
        }

        try {
            Integer width = call.argument("width");
            Integer height = call.argument("height");
            List<Map<String, Object>> planes = call.argument("planes");
            Integer deviceOrientation = call.argument("deviceOrientation");
            Integer sensorOrientation = call.argument("sensorOrientation");
            Boolean isFront = call.argument("isFront");

            if (width == null || height == null || planes == null) {
                result.error("INVALID_ARGS", "Missing required arguments for detect", null);
                return;
            }

            // Convert YUV420 to Bitmap
            Bitmap bitmap = convertYUV420ToBitmap(planes, width, height);
            if (bitmap == null) {
                result.error("CONVERSION_ERROR", "Failed to convert YUV to Bitmap", null);
                return;
            }

            // Apply orientation adjustments if needed
            if (deviceOrientation != null && sensorOrientation != null && isFront != null) {
                bitmap = applyOrientation(bitmap, deviceOrientation, sensorOrientation, isFront);
            }

            // Create MPImage and detect async
            MPImage image = new BitmapImageBuilder(bitmap).build();
            poseLandmarker.detectAsync(image, System.currentTimeMillis());
            
            // Async result will be handled by handlePoseResults callback
            result.success(null);
            
        } catch (Exception e) {
            Log.e(TAG, "Detection error", e);
            result.error("DETECT_ERROR", "Detection failed: " + e.getMessage(), null);
        }
    }

    private void detectRGBA(MethodCall call, Result result) {
        if (!isInitialized || poseLandmarker == null) {
            result.error("NOT_INITIALIZED", "PoseLandmarker not initialized", null);
            return;
        }

        try {
            Integer width = call.argument("width");
            Integer height = call.argument("height");
            byte[] imageData = call.argument("imageData");

            if (width == null || height == null || imageData == null) {
                result.error("INVALID_ARGS", "Missing required arguments for detectRGBA", null);
                return;
            }

            // Convert RGBA byte array to Bitmap
            Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
            ByteBuffer buffer = ByteBuffer.wrap(imageData);
            bitmap.copyPixelsFromBuffer(buffer);

            // Create MPImage and detect async
            MPImage image = new BitmapImageBuilder(bitmap).build();
            poseLandmarker.detectAsync(image, System.currentTimeMillis());
            
            // Async result will be handled by handlePoseResults callback
            result.success(null);
            
        } catch (Exception e) {
            Log.e(TAG, "RGBA detection error", e);
            result.error("DETECT_ERROR", "RGBA detection failed: " + e.getMessage(), null);
        }
    }

    private void resetInitializationState(Result result) {
        isInitialized = false;
        Log.d(TAG, "Initialization state reset");
        result.success(null);
    }

    private void release(Result result) {
        try {
            if (poseLandmarker != null) {
                poseLandmarker.close();
                poseLandmarker = null;
            }
            isInitialized = false;
            Log.d(TAG, "PoseLandmarker released");
            result.success(null);
        } catch (Exception e) {
            Log.e(TAG, "Error releasing PoseLandmarker", e);
            result.error("RELEASE_ERROR", "Failed to release: " + e.getMessage(), null);
        }
    }

    private void handlePoseResults(PoseLandmarkerResult result, MPImage image) {
        try {
            List<Map<String, Object>> landmarksData = new ArrayList<>();
            
            if (result.landmarks().isEmpty()) {
                // No pose detected
                channel.invokeMethod("onResult", landmarksData);
                return;
            }

            // Convert landmarks to Flutter-compatible format
            for (int i = 0; i < result.landmarks().size(); i++) {
                List<Map<String, Object>> poseLandmarks = new ArrayList<>();
                
                result.landmarks().get(i).forEach(landmark -> {
                    Map<String, Object> point = new HashMap<>();
                    point.put("x", landmark.x());
                    point.put("y", landmark.y());
                    point.put("z", landmark.z());
                    point.put("visibility", landmark.visibility().orElse(1.0f));
                    point.put("presence", landmark.presence().orElse(1.0f));
                    poseLandmarks.add(point);
                });
                
                Map<String, Object> poseData = new HashMap<>();
                poseData.put("landmarks", poseLandmarks);
                landmarksData.add(poseData);
            }

            Log.d(TAG, "Pose detection successful, poses found: " + landmarksData.size());
            channel.invokeMethod("onResult", landmarksData);
            
        } catch (Exception e) {
            Log.e(TAG, "Error processing pose results", e);
            Map<String, Object> errorData = new HashMap<>();
            errorData.put("error", e.getMessage());
            channel.invokeMethod("onError", errorData);
        }
    }

    private void handleError(RuntimeException error) {
        Log.e(TAG, "MediaPipe error", error);
        Map<String, Object> errorData = new HashMap<>();
        errorData.put("error", error.getMessage());
        channel.invokeMethod("onError", errorData);
    }

    private Bitmap convertYUV420ToBitmap(List<Map<String, Object>> planes, int width, int height) {
        try {
            if (planes.size() < 3) {
                return null;
            }

            // Get Y, U, V planes
            byte[] yPlane = (byte[]) planes.get(0).get("bytes");
            byte[] uPlane = (byte[]) planes.get(1).get("bytes");
            byte[] vPlane = (byte[]) planes.get(2).get("bytes");

            int ySize = yPlane.length;
            int uvSize = uPlane.length;

            // Create NV21 byte array (Y + VU interleaved)
            byte[] nv21 = new byte[ySize + uvSize * 2];
            System.arraycopy(yPlane, 0, nv21, 0, ySize);

            // Interleave V and U
            for (int i = 0; i < uvSize; i++) {
                nv21[ySize + i * 2] = vPlane[i];
                nv21[ySize + i * 2 + 1] = uPlane[i];
            }

            // Convert to Bitmap using YuvImage
            YuvImage yuv = new YuvImage(nv21, ImageFormat.NV21, width, height, null);
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            yuv.compressToJpeg(new Rect(0, 0, width, height), 90, out);
            byte[] imageBytes = out.toByteArray();
            
            return BitmapFactory.decodeByteArray(imageBytes, 0, imageBytes.length);
            
        } catch (Exception e) {
            Log.e(TAG, "Error converting YUV to Bitmap", e);
            return null;
        }
    }

    private Bitmap applyOrientation(Bitmap bitmap, int deviceOrientation, int sensorOrientation, boolean isFront) {
        // Apply rotation and mirroring based on orientation
        // This is a simplified implementation - you may need to adjust based on specific requirements
        return bitmap;
    }

    private String copyAssetToCache(String assetName) {
        try {
            File cacheFile = new File(context.getCacheDir(), assetName);
            if (cacheFile.exists()) {
                return cacheFile.getAbsolutePath();
            }

            InputStream inputStream = context.getAssets().open(assetName);
            FileOutputStream outputStream = new FileOutputStream(cacheFile);
            
            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, length);
            }
            
            outputStream.close();
            inputStream.close();
            
            Log.d(TAG, "Model file copied to cache: " + cacheFile.getAbsolutePath());
            return cacheFile.getAbsolutePath();
            
        } catch (IOException e) {
            Log.e(TAG, "Failed to copy asset to cache", e);
            return null;
        }
    }

    @Override
    public void onDetachedFromEngine(@NonNull FlutterPluginBinding binding) {
        channel.setMethodCallHandler(null);
        if (poseLandmarker != null) {
            poseLandmarker.close();
            poseLandmarker = null;
        }
        Log.d(TAG, "PoseLandmarkerPlugin detached from engine");
    }

    @Override
    public void onAttachedToActivity(@NonNull ActivityPluginBinding binding) {
        activity = binding.getActivity();
    }

    @Override
    public void onDetachedFromActivityForConfigChanges() {
        activity = null;
    }

    @Override
    public void onReattachedToActivityForConfigChanges(@NonNull ActivityPluginBinding binding) {
        activity = binding.getActivity();
    }

    @Override
    public void onDetachedFromActivity() {
        activity = null;
    }
}