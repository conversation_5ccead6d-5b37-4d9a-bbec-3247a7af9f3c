I/flutter (31432): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31432): │ #0   CustomApp.main.<anonymous closure> (package:keepdance/main.dart:117:16)
I/flutter (31432): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31432): │ 17:27:55.343 (+0:00:01.389750)
I/flutter (31432): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31432): │ ⛔ Flutter错误: SliverGeometry is not valid: The "layoutExtent" exceeds the "paintExtent".
I/flutter (31432): │ ⛔ The paintExtent is 0.0, but the layoutExtent is 107.2.
I/flutter (31432): │ ⛔ The RenderSliver that returned the offending geometry was: _RenderSliverPinnedPersistentHeaderForWidgets#16360 relayoutBoundary=up1 NEEDS-LAYOUT NEEDS-PAINT NEEDS-COMPOSITING-BITS-UPDATE:
I/flutter (31432): │ ⛔   creator: _SliverPinnedPersistentHeader ← SliverPersistentHeader ← Viewport ← IgnorePointer-[GlobalKey#d993d] ← Semantics ← Listener ← _GestureSemantics ← RawGestureDetector-[LabeledGlobalKey<RawGestureDetectorState>#a3805] ← Listener ← _ScrollableScope ← _ScrollSemantics-[GlobalKey#f3a8f] ← NotificationListener<ScrollMetricsNotification> ← ⋯
I/flutter (31432): │ ⛔   parentData: paintOffset=Offset(0.0, 0.0) (can use size)
I/flutter (31432): │ ⛔   constraints: SliverConstraints(AxisDirection.down, GrowthDirection.forward, ScrollDirection.idle, scrollOffset: 0.0, precedingScrollExtent: 0.0, remainingPaintExtent: 740.9, crossAxisExtent: 387.7, crossAxisDirection: AxisDirection.right, viewportMainAxisExtent: 740.9, remainingCacheExtent: 990.9, cacheOrigin: 0.0)
I/flutter (31432): │ ⛔   geometry: SliverGeometry(scrollExtent: 107.2, hidden, layoutExtent: 107.2, maxPaintExtent: 107.2, hasVisualOverflow: true, cacheExtent: 107.2)
I/flutter (31432): │ ⛔   maxExtent: 107.2
I/flutter (31432): │ ⛔   child position: 0.0
I/flutter (31432): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31432): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31432): │ #0   CustomApp.main.<anonymous closure> (package:keepdance/main.dart:117:16)
I/flutter (31432): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31432): │ 17:27:55.379 (+0:00:01.425898)
I/flutter (31432): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31432): │ ⛔ Flutter错误: SliverGeometry is not valid: The "layoutExtent" exceeds the "paintExtent".
I/flutter (31432): │ ⛔ The paintExtent is 0.0, but the layoutExtent is 107.2.
I/flutter (31432): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31432): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31432): │ #0   CustomApp.main.<anonymous closure> (package:keepdance/main.dart:117:16)
I/flutter (31432): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31432): │ 17:27:55.466 (+0:00:01.513162)
I/flutter (31432): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31432): │ ⛔ Flutter错误: Null check operator used on a null value
I/flutter (31432): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────

I/flutter (31432): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31432): │ #0   CustomApp.main.<anonymous closure> (package:keepdance/main.dart:188:16)
I/flutter (31432): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31432): │ 17:27:55.552 (+0:00:01.599279)
I/flutter (31432): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31432): │ ⛔ 全局错误: LateInitializationError: Field '_logger@93008013' has not been initialized.
I/flutter (31432): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────


I/flutter (31432): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31432): │ #0   CustomApp.main.<anonymous closure> (package:keepdance/main.dart:188:16)
I/flutter (31432): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31432): │ 17:27:55.566 (+0:00:01.613190)
I/flutter (31432): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31432): │ ⛔ 全局错误: LateInitializationError: Field '_logger@95140909' has not been initialized.
I/flutter (31432): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31432): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31432): │ #0   CustomApp.main.<anonymous closure> (package:keepdance/main.dart:117:16)
I/flutter (31432): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31432): │ 17:27:55.623 (+0:00:01.669397)
I/flutter (31432): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31432): │ ⛔ Flutter错误: SliverGeometry is not valid: The "layoutExtent" exceeds the "paintExtent".
I/flutter (31432): │ ⛔ The paintExtent is 0.0, but the layoutExtent is 107.2.
I/flutter (31432): │ ⛔ The RenderSliver that returned the offending geometry was: _RenderSliverPinnedPersistentHeaderForWidgets#16360 relayoutBoundary=up1 NEEDS-LAYOUT NEEDS-PAINT:
I/flutter (31432): │ ⛔   needs compositing
I/flutter (31432): │ ⛔   creator: _SliverPinnedPersistentHeader ← SliverPersistentHeader ← Viewport ← IgnorePointer-[GlobalKey#d993d] ← Semantics ← Listener ← _GestureSemantics ← RawGestureDetector-[LabeledGlobalKey<RawGestureDetectorState>#a3805] ← Listener ← _ScrollableScope ← _ScrollSemantics-[GlobalKey#f3a8f] ← NotificationListener<ScrollMetricsNotification> ← ⋯
I/flutter (31432): │ ⛔   parentData: paintOffset=Offset(0.0, 0.0) (can use size)
I/flutter (31432): │ ⛔   constraints: SliverConstraints(AxisDirection.down, GrowthDirection.forward, ScrollDirection.idle, scrollOffset: 0.0, precedingScrollExtent: 0.0, remainingPaintExtent: 740.9, crossAxisExtent: 387.7, crossAxisDirection: AxisDirection.right, viewportMainAxisExtent: 740.9, remainingCacheExtent: 990.9, cacheOrigin: 0.0)
I/flutter (31432): │ ⛔   geometry: SliverGeometry(scrollExtent: 107.2, hidden, layoutExtent: 107.2, maxPaintExtent: 107.2, hasVisualOverflow: true, cacheExtent: 107.2)
I/flutter (31432): │ ⛔   maxExtent: 107.2
I/flutter (31432): │ ⛔   child position: 0.0
I/flutter (31432): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31432): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31432): │ #0   CustomApp.main.<anonymous closure> (package:keepdance/main.dart:117:16)
I/flutter (31432): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31432): │ 17:27:55.626 (+0:00:01.672295)
I/flutter (31432): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31432): │ ⛔ Flutter错误: SliverGeometry is not valid: The "layoutExtent" exceeds the "paintExtent".
I/flutter (31432): │ ⛔ The paintExtent is 0.0, but the layoutExtent is 107.2.
I/flutter (31432): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31432): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31432): │ #0   CustomApp.main.<anonymous closure> (package:keepdance/main.dart:117:16)
I/flutter (31432): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31432): │ 17:27:55.627 (+0:00:01.673778)
I/flutter (31432): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31432): │ ⛔ Flutter错误: Null check operator used on a null value
I/flutter (31432): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/DecorView(31432): updateColorViewInt type:0 size: 130 color:40000000 appColor:40000000
I/flutter (31432): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31432): │ #0   HomeController._initializeAsync (package:keepdance/pages/home/<USER>/home_controller.dart:152:15)
I/flutter (31432): │ #1   <asynchronous suspension>
I/flutter (31432): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31432): │ ⛔ HomeController异步初始化失败: Could not find a generator for route RouteSettings("/login", null) in the _WidgetsAppState.
I/flutter (31432): │ ⛔ Make sure your root app widget has provided a way to generate
I/flutter (31432): │ ⛔ this route.
I/flutter (31432): │ ⛔ Generators for routes are searched for in the following order:
I/flutter (31432): │ ⛔  1. For the "/" route, the "home" property, if non-null, is used.
I/flutter (31432): │ ⛔  2. Otherwise, the "routes" table is used, if it has an entry for the route.
I/flutter (31432): │ ⛔  3. Otherwise, onGenerateRoute is called. It should return a non-null value for any valid route not handled by "home" and "routes".
I/flutter (31432): │ ⛔  4. Finally if all else fails onUnknownRoute is called.
I/flutter (31432): │ ⛔ Unfortunately, onUnknownRoute was not set.
I/flutter (31432): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/DecorView(31432): updateColorViewInt type:0 size: 130 color:40000000 appColor:40000000
I/DecorView[](31432): pkgName:com.example.legend_dance old windowMode:1 new windoMode:1, isFixedSize:false, isStackNeedCaptionView:true
I/DecorView(31432): updateColorViewInt type:0 size: 130 color:40000000 appColor:40000000
I/DecorView(31432): updateColorViewInt type:0 size: 130 color:40000000 appColor:40000000
I/HwViewRootImpl(31432): removeInvalidNode jank list is null
I/flutter (31432): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31432): │ #0   BaseAnalytics.trackEventSafely (package:keepdance/utils/analytics/base_analytics.dart:107:15)
I/flutter (31432): │ #1   PageAnalytics._trackPageView (package:keepdance/utils/analytics/page_analytics.dart:60:21)
I/flutter (31432): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31432): │ ! BaseAnalytics 未初始化，正在初始化...
I/flutter (31432): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
D/HwViewRootImpl(31432): [DetectViewsLocationRunner] start to getViewHierarchy
I/AwareBitmapCacher(31432): init lrucache size: 4194304 pid=31432
D/HwViewRootImpl(31432): [DetectViewsLocationRunner] deviceOrientation: 0
D/HwViewRootImpl(31432): [DetectViewsLocationRunner-ScreenDirection] ROTATION_0
D/HwViewRootImpl(31432): [DetectViewsLocationRunner] get views' rect = 0, SCENE_WINDOW_FOCUS_CHANGE


I/flutter (31432): ┌───────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31432): │ #0   DeviceInfoService.getDeviceUniqueId (package:keepdance/services/device_info_service.dart:80:15)
I/flutter (31432): │ #1   ApiClient.getHeaders (package:keepdance/core/network/api_client.dart:302:42)
I/flutter (31432): │ #2   <asynchronous suspension>
I/flutter (31432): │ #3   ApiClient.getEnhanced (package:keepdance/core/network/api_client.dart:130:23)
I/flutter (31432): │ #4   <asynchronous suspension>
I/flutter (31432): │ #5   ApiClient.get (package:keepdance/core/network/api_client.dart:109:30)
I/flutter (31432): │ #6   <asynchronous suspension>
I/flutter (31432): │ #7   LocationService._fetchAndSaveLocation (package:keepdance/utils/location_service.dart:90:24)
I/flutter (31432): │ #8   <asynchronous suspension>
I/flutter (31432): │ #9   LocationService.getLocation (package:keepdance/utils/location_service.dart:69:16)
I/flutter (31432): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31432): │ 17:28:00.839 (+0:00:06.885772)
I/flutter (31432): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31432): │ ⛔ 获取设备唯一标识符失败: type 'PrivacyComplianceService' is not a subtype of type 'PrivacyComplianceService' in type cast where
I/flutter (31432): │ ⛔   PrivacyComplianceService is from package:keepdance/services/privacy_compliance_service.dart
I/flutter (31432): │ ⛔   PrivacyComplianceService is from package:keepdance/services/device_info_service.dart
I/flutter (31432): │ ⛔
I/flutter (31432): └───────────────────────────────────────────────────────────────────────────────────────────────────




I/flutter (31432): Another exception was thrown: FormatException: Message corrupted
I/flutter (31432): ┌───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
I/flutter (31432): │ #0   CustomApp.main.<anonymous closure> (package:keepdance/main.dart:117:16)
I/flutter (31432): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31432): │ 17:28:18.329 (+0:00:24.375847)
I/flutter (31432): ├┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄┄
I/flutter (31432): │ ⛔ Flutter错误: FormatException: Message corrupted
I/flutter (31432): └───────────────────────────────────────────────────────────────────────────────────────────────────────────────────────
