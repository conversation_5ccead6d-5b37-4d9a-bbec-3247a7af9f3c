#
# To learn more about a Podspec see http://guides.cocoapods.org/syntax/podspec.html.
# Run `pod lib lint pose_landmarker_plugin.podspec` to validate before publishing.
#
Pod::Spec.new do |s|
  s.name             = 'pose_landmarker_plugin'
  s.version          = '0.0.1'
  s.summary          = 'A Flutter plugin for pose detection using MediaPipe.'
  s.description      = <<-DESC
A Flutter plugin that provides pose detection capabilities using Google MediaPipe Pose Landmarker.
                       DESC
  s.homepage         = 'http://example.com'
  s.license          = { :file => '../LICENSE' }
  s.author           = { 'Your Company' => '<EMAIL>' }
  s.source           = { :path => '.' }
  s.source_files = 'Classes/**/*'
  s.dependency 'Flutter'
  s.dependency 'MediaPipeTasksVision', '~> 0.10.8'
  s.platform = :ios, '12.0'

  # Flutter.framework does not contain a i386 slice.
  s.pod_target_xcconfig = { 'DEFINES_MODULE' => 'YES', 'EXCLUDED_ARCHS[sdk=iphonesimulator*]' => 'i386' }
  s.swift_version = '5.0'
end