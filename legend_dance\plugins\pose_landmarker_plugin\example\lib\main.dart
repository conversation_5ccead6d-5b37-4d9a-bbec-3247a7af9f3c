import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:pose_landmarker_plugin/pose_landmarker_plugin.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  // Get available cameras
  final cameras = await availableCameras();
  
  runApp(MyApp(cameras: cameras));
}

class MyApp extends StatelessWidget {
  final List<CameraDescription> cameras;
  
  const MyApp({super.key, required this.cameras});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Pose Landmarker Demo',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: PoseDetectionPage(cameras: cameras),
    );
  }
}

class PoseDetectionPage extends StatefulWidget {
  final List<CameraDescription> cameras;
  
  const PoseDetectionPage({super.key, required this.cameras});

  @override
  State<PoseDetectionPage> createState() => _PoseDetectionPageState();
}

class _PoseDetectionPageState extends State<PoseDetectionPage> {
  CameraController? controller;
  bool isDetecting = false;
  List<PoseDetectionResult> currentResults = [];
  String? errorMessage;
  
  @override
  void initState() {
    super.initState();
    _initializeCamera();
    _initializePoseDetection();
  }

  Future<void> _initializeCamera() async {
    if (widget.cameras.isEmpty) {
      setState(() {
        errorMessage = 'No cameras available';
      });
      return;
    }

    controller = CameraController(
      widget.cameras[0],
      ResolutionPreset.medium,
      enableAudio: false,
    );

    try {
      await controller!.initialize();
      setState(() {});
    } catch (e) {
      setState(() {
        errorMessage = 'Camera initialization failed: $e';
      });
    }
  }

  Future<void> _initializePoseDetection() async {
    try {
      // Initialize pose landmarker
      await PoseLandmarkerPlugin.initialize(maxPoses: 1);
      
      // Listen for results
      PoseLandmarkerPlugin.onResult.listen((results) {
        setState(() {
          currentResults = results;
        });
      });
      
      // Listen for errors
      PoseLandmarkerPlugin.onError.listen((error) {
        setState(() {
          errorMessage = error;
        });
      });
      
    } catch (e) {
      setState(() {
        errorMessage = 'Pose detection initialization failed: $e';
      });
    }
  }

  void _startDetection() async {
    if (controller == null || !controller!.value.isInitialized) return;
    
    setState(() {
      isDetecting = true;
      errorMessage = null;
    });

    await controller!.startImageStream((CameraImage image) async {
      if (!isDetecting) return;

      if (image.format.group == ImageFormatGroup.yuv420) {
        try {
          await PoseLandmarkerPlugin.detect(
            width: image.width,
            height: image.height,
            planes: image.planes.map((plane) => {
              'bytes': plane.bytes,
            }).toList(),
            deviceOrientation: MediaQuery.of(context).orientation == Orientation.portrait ? 0 : 90,
            sensorOrientation: controller!.description.sensorOrientation,
            isFront: controller!.description.lensDirection == CameraLensDirection.front,
          );
        } catch (e) {
          setState(() {
            errorMessage = 'Detection failed: $e';
          });
        }
      }
    });
  }

  void _stopDetection() async {
    setState(() {
      isDetecting = false;
    });
    
    if (controller != null) {
      await controller!.stopImageStream();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (controller == null || !controller!.value.isInitialized) {
      return Scaffold(
        appBar: AppBar(title: const Text('Pose Detection Demo')),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Pose Detection Demo'),
        actions: [
          IconButton(
            icon: Icon(isDetecting ? Icons.stop : Icons.play_arrow),
            onPressed: isDetecting ? _stopDetection : _startDetection,
          ),
        ],
      ),
      body: Column(
        children: [
          // Camera Preview
          Expanded(
            flex: 3,
            child: Stack(
              children: [
                CameraPreview(controller!),
                if (currentResults.isNotEmpty)
                  CustomPaint(
                    painter: PosePainter(currentResults.first),
                    size: Size.infinite,
                  ),
              ],
            ),
          ),
          
          // Detection Info
          Expanded(
            flex: 1,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              color: Colors.black87,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Status: ${isDetecting ? "Detecting" : "Stopped"}',
                    style: const TextStyle(color: Colors.white, fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Poses Detected: ${currentResults.length}',
                    style: const TextStyle(color: Colors.white),
                  ),
                  if (currentResults.isNotEmpty) ...[
                    const SizedBox(height: 4),
                    Text(
                      'Visibility: ${(currentResults.first.averageVisibility * 100).toStringAsFixed(1)}%',
                      style: const TextStyle(color: Colors.white),
                    ),
                    Text(
                      'Landmarks: ${currentResults.first.landmarks.length}',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ],
                  if (errorMessage != null) ...[
                    const SizedBox(height: 8),
                    Text(
                      'Error: $errorMessage',
                      style: const TextStyle(color: Colors.red),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    controller?.dispose();
    PoseLandmarkerPlugin.dispose();
    super.dispose();
  }
}

// Custom painter for drawing pose landmarks
class PosePainter extends CustomPainter {
  final PoseDetectionResult result;
  
  PosePainter(this.result);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.red
      ..strokeWidth = 2
      ..style = PaintingStyle.fill;

    // Draw landmarks as circles
    for (final landmark in result.landmarks) {
      final x = landmark.x * size.width;
      final y = landmark.y * size.height;
      
      if (landmark.visibility > 0.5) {
        canvas.drawCircle(Offset(x, y), 4, paint);
      }
    }

    // Draw some basic connections (simplified skeleton)
    final linePaint = Paint()
      ..color = Colors.blue
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    _drawConnection(canvas, size, linePaint, 
      PoseLandmarkType.leftShoulder, PoseLandmarkType.rightShoulder);
    _drawConnection(canvas, size, linePaint,
      PoseLandmarkType.leftShoulder, PoseLandmarkType.leftElbow);
    _drawConnection(canvas, size, linePaint,
      PoseLandmarkType.leftElbow, PoseLandmarkType.leftWrist);
    _drawConnection(canvas, size, linePaint,
      PoseLandmarkType.rightShoulder, PoseLandmarkType.rightElbow);
    _drawConnection(canvas, size, linePaint,
      PoseLandmarkType.rightElbow, PoseLandmarkType.rightWrist);
  }

  void _drawConnection(Canvas canvas, Size size, Paint paint,
      PoseLandmarkType start, PoseLandmarkType end) {
    final startLandmark = result.getLandmark(start);
    final endLandmark = result.getLandmark(end);
    
    if (startLandmark != null && endLandmark != null &&
        startLandmark.visibility > 0.5 && endLandmark.visibility > 0.5) {
      final startPoint = Offset(
        startLandmark.x * size.width,
        startLandmark.y * size.height,
      );
      final endPoint = Offset(
        endLandmark.x * size.width,
        endLandmark.y * size.height,
      );
      
      canvas.drawLine(startPoint, endPoint, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}