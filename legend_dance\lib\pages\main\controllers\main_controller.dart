import 'package:get/get.dart';
import 'package:logger/logger.dart';

class MainController extends GetxController {
  static final Logger _logger = Logger();

  // 响应式状态管理
  final RxInt currentPageIndex = 0.obs;
  final RxBool isInitialized = false.obs;
  final RxString appVersion = "1.0.0".obs;
  final RxMap<String, dynamic> globalSettings = <String, dynamic>{}.obs;

  // 页面标题
  final List<String> pageTitles = ['首页', '发现', '工作坊', '我的'];

  @override
  void onInit() {
    super.onInit();
    _logger.i("MainController - onInit 开始初始化");

    // 初始化全局设置
    _initializeGlobalSettings();
  }

  /// 初始化全局设置
  void _initializeGlobalSettings() async {
    try {
      // 模拟加载全局设置
      globalSettings.assignAll({
        'theme': 'light',
        'language': 'zh_CN',
        'notifications': true,
        'autoPlay': false,
        'qualityPreference': 'high',
      });

      isInitialized.value = true;
      _logger.i("全局设置初始化完成");
    } catch (e) {
      _logger.e("初始化全局设置失败: $e");
    }
  }

  /// 切换页面
  void changePage(int index) {
    if (index >= 0 && index < pageTitles.length) {
      currentPageIndex.value = index;
      _logger.d("切换到页面: ${pageTitles[index]} (index: $index)");
    }
  }

  /// 获取当前页面标题
  String get currentPageTitle => pageTitles[currentPageIndex.value];

  /// 更新全局设置
  void updateGlobalSetting(String key, dynamic value) {
    globalSettings[key] = value;
    _logger.d("更新全局设置: $key = $value");

    // 这里可以持久化保存设置
    _saveSettings();
  }

  /// 保存设置到本地存储
  void _saveSettings() {
    // 实现设置持久化逻辑
    _logger.d("保存全局设置到本地存储");
  }

  /// 获取全局设置值
  T? getGlobalSetting<T>(String key) {
    return globalSettings[key] as T?;
  }

  /// 重置所有设置为默认值
  void resetToDefaults() {
    globalSettings.assignAll({
      'theme': 'light',
      'language': 'zh_CN',
      'notifications': true,
      'autoPlay': false,
      'qualityPreference': 'high',
    });
    _logger.i("全局设置已重置为默认值");

    _saveSettings();
  }

  /// 检查是否为首次启动
  bool get isFirstLaunch {
    return getGlobalSetting<bool>('isFirstLaunch') ?? true;
  }

  /// 标记已完成首次启动
  void markFirstLaunchCompleted() {
    updateGlobalSetting('isFirstLaunch', false);
  }

  @override
  void onClose() {
    _logger.i("MainController - 控制器销毁");
    super.onClose();
  }
}
