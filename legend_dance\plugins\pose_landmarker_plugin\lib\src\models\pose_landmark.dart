part of pose_landmarker_plugin;

/// Represents a single pose landmark with 3D coordinates and confidence scores
class PoseLandmark {
  /// Normalized x coordinate (0.0 to 1.0)
  final double x;
  
  /// Normalized y coordinate (0.0 to 1.0) 
  final double y;
  
  /// Normalized z coordinate (depth)
  final double z;
  
  /// Visibility confidence score (0.0 to 1.0)
  final double visibility;
  
  /// Presence confidence score (0.0 to 1.0)
  final double presence;

  const PoseLandmark({
    required this.x,
    required this.y,
    required this.z,
    required this.visibility,
    required this.presence,
  });

  factory PoseLandmark.fromMap(Map<String, dynamic> map) {
    return PoseLandmark(
      x: (map['x'] as num?)?.toDouble() ?? 0.0,
      y: (map['y'] as num?)?.toDouble() ?? 0.0,
      z: (map['z'] as num?)?.toDouble() ?? 0.0,
      visibility: (map['visibility'] as num?)?.toDouble() ?? 1.0,
      presence: (map['presence'] as num?)?.toDouble() ?? 1.0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'x': x,
      'y': y,
      'z': z,
      'visibility': visibility,
      'presence': presence,
    };
  }

  @override
  String toString() {
    return 'PoseLandmark(x: $x, y: $y, z: $z, visibility: $visibility, presence: $presence)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PoseLandmark &&
        other.x == x &&
        other.y == y &&
        other.z == z &&
        other.visibility == visibility &&
        other.presence == presence;
  }

  @override
  int get hashCode {
    return x.hashCode ^
        y.hashCode ^
        z.hashCode ^
        visibility.hashCode ^
        presence.hashCode;
  }
}

/// Enum representing the 33 pose landmark indices based on MediaPipe Pose
enum PoseLandmarkType {
  nose(0),
  leftEyeInner(1),
  leftEye(2),
  leftEyeOuter(3),
  rightEyeInner(4),
  rightEye(5),
  rightEyeOuter(6),
  leftEar(7),
  rightEar(8),
  mouthLeft(9),
  mouthRight(10),
  leftShoulder(11),
  rightShoulder(12),
  leftElbow(13),
  rightElbow(14),
  leftWrist(15),
  rightWrist(16),
  leftPinky(17),
  rightPinky(18),
  leftIndex(19),
  rightIndex(20),
  leftThumb(21),
  rightThumb(22),
  leftHip(23),
  rightHip(24),
  leftKnee(25),
  rightKnee(26),
  leftAnkle(27),
  rightAnkle(28),
  leftHeel(29),
  rightHeel(30),
  leftFootIndex(31),
  rightFootIndex(32);

  const PoseLandmarkType(this.index);
  
  final int index;

  /// Get landmark by index
  static PoseLandmarkType? fromIndex(int index) {
    for (PoseLandmarkType type in PoseLandmarkType.values) {
      if (type.index == index) return type;
    }
    return null;
  }
}