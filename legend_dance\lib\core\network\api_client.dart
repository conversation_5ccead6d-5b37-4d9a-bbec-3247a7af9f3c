// lib: , url: package:keepdance/core/network/api_client.dart

import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:logger/logger.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

// 假设这些服务类在项目的其他地方定义
import 'package:keepdance/services/cache_service.dart';
import 'package:keepdance/services/device_info_service.dart';

/// API 错误类型枚举
/// 根据 _analyzeErrorType 和 _getErrorMessage 方法推断得出
enum ApiErrorType {
  /// 未知错误
  unknown,

  /// 业务逻辑错误 (由后端返回非成功code)
  business,

  /// 未授权 (Token过期等, HTTP 401/403)
  unauthorized,

  /// 网络连接错误
  network,

  /// 服务器内部错误 (HTTP 5xx)
  serverError,

  /// 请求超时
  timeout,

  /// 数据解析错误
  parseError,
}

/// API 请求结果的封装类
/// 字段根据 toMap 方法中的 key 推断
class ApiResult<T> {
  /// 业务状态码 (例如, 0代表成功)
  final int code;

  /// 响应数据
  final T? data;

  /// 响应消息
  final String? msg;

  /// 错误类型
  final ApiErrorType? errorType;

  /// 详细错误信息 (用于日志)
  final String? errorDetails;

  /// HTTP 状态码
  final int? httpStatusCode;

  ApiResult({
    required this.code,
    this.data,
    this.msg,
    this.errorType,
    this.errorDetails,
    this.httpStatusCode,
  });

  /// 将结果对象转换为Map，通常用于返回给调用层
  /// 根据汇编代码 0x88d384 (ApiResult.toMap) 还原
  Map<String, dynamic> toMap() {
    return {
      'code': code,
      'data': data,
      'msg': msg,
      'error_type': errorType?.name,
      'error_details': errorDetails,
      'http_status_code': httpStatusCode,
    };
  }
}

/// API 客户端，负责处理所有网络请求
class ApiClient extends GetxService {
  // 根据 0x71a12c 的单例模式实现还原
  static final ApiClient instance = ApiClient._internal();
  factory ApiClient() => instance;

  late final String _baseUrl;
  late final Logger _logger;
  final List<Map<String, dynamic>> _logBuffer = [];

  // 根据 0x88d820 的 timeout 调用推断，设定一个合理的默认超时时间
  static const Duration _timeout = Duration(seconds: 15);

  /// 私有构造函数，用于初始化
  /// 根据汇编代码 0x71a16c (ApiClient._internal) 还原
  ApiClient._internal() {
    // 0x71a178: "https://saas.api.tiaotiaomaster.cn"
    _baseUrl = "https://saas.api.tiaotiaomaster.cn";

    // 0x71a194: 初始化 PrettyPrinter 和 Logger
    _logger = Logger(
      printer: PrettyPrinter(
        methodCount: 10,
        errorMethodCount: 100,
        lineLength: 120,
        colors: true,
        printEmojis: true,
        printTime: true,
      ),
    );
  }

  /// 发起 GET 请求
  /// 根据汇编代码 0x88d32c 还原
  Future<Map<String, dynamic>> get(
    String url, {
    Map<String, dynamic>? queryParameters,
  }) async {
    // 此方法为 getEnhanced 的一层封装
    final ApiResult result = await getEnhanced(
      url,
      queryParameters: queryParameters,
    );
    return result.toMap();
  }

  /// 发起 POST 请求
  /// 根据汇编代码 0x898314 还原
  Future<Map<String, dynamic>> post(
    String url, {
    dynamic data,
    Map<String, String>? additionalHeaders,
  }) async {
    // 此方法为 postEnhanced 的一层封装
    final ApiResult result = await postEnhanced(
      url,
      data: data,
      additionalHeaders: additionalHeaders,
    );
    return result.toMap();
  }

  /// 增强的 GET 请求，包含完整的错误处理和日志记录逻辑
  /// 根据汇编代码 0x88d568 还原
  Future<ApiResult> getEnhanced(
    String url, {
    Map<String, dynamic>? queryParameters,
  }) async {
    final DateTime startTime = DateTime.now();
    // TODO: 实际实现中应将 queryParameters 拼接到URL
    final String fullUrl = '$_baseUrl$url';

    http.Response? response;
    try {
      final headers = await getHeaders();

      response = await http
          .get(Uri.parse(fullUrl), headers: headers)
          .timeout(_timeout);

      final int statusCode = response.statusCode;

      if (statusCode == 200) {
        try {
          final String responseBody = utf8.decode(response.bodyBytes);
          final Map<String, dynamic> responseData = json.decode(responseBody);
          final result = _createSuccessResult(responseData);
          _logRequestMetrics(
            method: 'GET',
            url: fullUrl,
            startTime: startTime,
            endTime: DateTime.now(),
            httpStatusCode: statusCode,
            error: result.code != 0 ? result.errorDetails : null,
            responseData: responseData,
          );
          return result;
        } catch (e) {
          // JSON解析失败，可能是响应格式错误
          _logger.e('响应数据解析失败: $e');
          final ApiErrorType errorType = ApiErrorType.parseError;
          final String errorMessage = '服务器响应格式错误';
          final result = _createErrorResult(
            code: -1, // 解析错误使用-1作为错误码
            msg: errorMessage,
            errorType: errorType,
            errorDetails: e.toString(),
            httpStatusCode: statusCode,
          );
          _logRequestMetrics(
            method: 'GET',
            url: fullUrl,
            startTime: startTime,
            endTime: DateTime.now(),
            httpStatusCode: statusCode,
            error: errorMessage,
            responseData: null,
          );
          return result;
        }
      } else {
        // HTTP 状态码非 200 的错误处理
        final ApiErrorType errorType = _analyzeErrorType(statusCode, null);
        final String errorMessage = _getErrorMessage(errorType, statusCode);
        final String errorDetails =
            "HTTP $statusCode: ${response.reasonPhrase}";

        final result = _createErrorResult(
          code: statusCode,
          msg: errorMessage,
          errorType: errorType,
          errorDetails: errorDetails,
          httpStatusCode: statusCode,
        );
        _logRequestMetrics(
          method: 'GET',
          url: fullUrl,
          startTime: startTime,
          endTime: DateTime.now(),
          httpStatusCode: statusCode,
          error: errorDetails,
          responseData: response.body,
        );
        return result;
      }
    } catch (e, s) {
      // 捕获请求过程中的所有异常 (如超时、网络中断)
      _logger.e("GET请求异常: $fullUrl: $e");
      final int statusCode = response?.statusCode ?? -1;
      final ApiErrorType errorType = _analyzeErrorType(statusCode, e);
      final String errorMessage = _getErrorMessage(errorType, statusCode);

      final result = _createErrorResult(
        code: statusCode,
        msg: errorMessage,
        errorType: errorType,
        errorDetails: e.toString(),
        httpStatusCode: statusCode,
      );
      _logRequestMetrics(
        method: 'GET',
        url: fullUrl,
        startTime: startTime,
        endTime: DateTime.now(),
        httpStatusCode: statusCode,
        error: e.toString(),
        responseData: null,
      );
      return result;
    }
  }

  /// 增强的 POST 请求，包含完整的错误处理和日志记录逻辑
  /// 根据汇编代码 0x8983b8 还原
  Future<ApiResult> postEnhanced(
    String url, {
    dynamic data,
    Map<String, String>? additionalHeaders,
  }) async {
    final DateTime startTime = DateTime.now();
    final String fullUrl = '$_baseUrl$url';

    http.Response? response;
    try {
      final headers = await getHeaders();
      if (additionalHeaders != null) {
        headers.addAll(additionalHeaders);
      }
      final String body = json.encode(data);

      response = await http
          .post(Uri.parse(fullUrl), headers: headers, body: body)
          .timeout(_timeout);

      final int statusCode = response.statusCode;

      if (statusCode == 200) {
        try {
          final String responseBody = utf8.decode(response.bodyBytes);
          final Map<String, dynamic> responseData = json.decode(responseBody);
          final result = _createSuccessResult(responseData);
          _logRequestMetrics(
            method: 'POST',
            url: fullUrl,
            startTime: startTime,
            endTime: DateTime.now(),
            httpStatusCode: statusCode,
            error: result.code != 0 ? result.errorDetails : null,
            responseData: responseData,
          );
          return result;
        } catch (e) {
          // JSON解析失败，可能是响应格式错误
          _logger.e('POST响应数据解析失败: $e');
          final ApiErrorType errorType = ApiErrorType.parseError;
          final String errorMessage = '服务器响应格式错误';
          final result = _createErrorResult(
            code: -1,
            msg: errorMessage,
            errorType: errorType,
            errorDetails: e.toString(),
            httpStatusCode: statusCode,
          );
          _logRequestMetrics(
            method: 'POST',
            url: fullUrl,
            startTime: startTime,
            endTime: DateTime.now(),
            httpStatusCode: statusCode,
            error: errorMessage,
            responseData: null,
          );
          return result;
        }
      } else {
        // HTTP 状态码非 200 的错误处理
        final ApiErrorType errorType = _analyzeErrorType(statusCode, null);
        final String errorMessage = _getErrorMessage(errorType, statusCode);
        final String errorDetails =
            "HTTP $statusCode: ${response.reasonPhrase}";

        final result = _createErrorResult(
          code: statusCode,
          msg: errorMessage,
          errorType: errorType,
          errorDetails: errorDetails,
          httpStatusCode: statusCode,
        );
        _logRequestMetrics(
          method: 'POST',
          url: fullUrl,
          startTime: startTime,
          endTime: DateTime.now(),
          httpStatusCode: statusCode,
          error: errorDetails,
          responseData: response.body,
        );
        return result;
      }
    } catch (e, s) {
      // 捕获请求过程中的所有异常
      _logger.e("POST请求异常: $fullUrl: $e");
      final int statusCode = response?.statusCode ?? -1;
      final ApiErrorType errorType = _analyzeErrorType(statusCode, e);
      final String errorMessage = _getErrorMessage(errorType, statusCode);

      final result = _createErrorResult(
        code: statusCode,
        msg: errorMessage,
        errorType: errorType,
        errorDetails: e.toString(),
        httpStatusCode: statusCode,
      );
      _logRequestMetrics(
        method: 'POST',
        url: fullUrl,
        startTime: startTime,
        endTime: DateTime.now(),
        httpStatusCode: statusCode,
        error: e.toString(),
        responseData: null,
      );
      return result;
    }
  }

  /// 构建统一的请求头
  /// 根据汇编代码 0x986ce0 还原
  Future<Map<String, String>> getHeaders() async {
    final cacheService = Get.find<CacheService>();
    final accessToken = cacheService.getAccessTokenSync();
    final refreshToken = cacheService.getRefreshTokenSync();

    final prefs = await SharedPreferences.getInstance();
    String cookie = prefs.getString('cookie') ?? '';
    String deviceId = prefs.getString('device_id') ?? '';

    if (deviceId.isEmpty) {
      final deviceInfoService = DeviceInfoService();
      deviceId = await deviceInfoService.getDeviceUniqueId();
      await prefs.setString('device_id', deviceId);
    }

    return {
      'Content-Type': 'application/json',
      'access-token': accessToken,
      'appChannel': 'phone_jxhy',
      'device_sn': deviceId,
      'refresh-token': refreshToken,
      'Cookie': cookie,
    };
  }

  /// 从成功的响应数据中创建 ApiResult
  /// 根据汇编代码 0x88e750 还原
  ApiResult _createSuccessResult(Map<String, dynamic> responseData) {
    final int code = responseData['code'] as int? ?? -2; // -2为汇编中未找到code时的默认值

    if (code == 0) {
      // 假设0为业务成功码
      return ApiResult(
        code: code,
        data: responseData['data'],
        msg: responseData['msg'] as String?,
        httpStatusCode: 200,
      );
    } else {
      // 处理业务逻辑错误 (HTTP 200, 但业务code非0)
      return ApiResult(
        code: code,
        data: responseData['data'],
        msg: responseData['msg'] as String?,
        errorType: ApiErrorType.business,
        errorDetails:
            'Business logic error: code=$code, msg=${responseData['msg']}',
        httpStatusCode: 200,
      );
    }
  }

  /// 创建一个表示错误的 ApiResult
  /// 根据汇编代码 0x88dd04 还原
  ApiResult _createErrorResult({
    required int code,
    required String msg,
    required ApiErrorType errorType,
    String? errorDetails,
    int? httpStatusCode,
  }) {
    return ApiResult(
      code: code,
      data: null,
      msg: msg,
      errorType: errorType,
      errorDetails: errorDetails,
      httpStatusCode: httpStatusCode,
    );
  }

  /// 根据错误类型和HTTP状态码获取用户友好的错误消息
  /// 根据汇编代码 0x88dd68 还原
  String _getErrorMessage(ApiErrorType errorType, int httpStatusCode) {
    switch (errorType) {
      case ApiErrorType.network:
        return "网络连接异常，请检查网络后重试";
      case ApiErrorType.unauthorized:
        return "登录已过期，请重新登录";
      case ApiErrorType.business:
        return "操作失败，请稍后重试"; // 业务错误的具体消息通常由后端返回
      case ApiErrorType.serverError:
        return "服务器异常，请稍后重试";
      case ApiErrorType.timeout:
        return "请求超时，请检查网络后重试";
      case ApiErrorType.parseError:
        return "数据解析失败，请稍后重试";
      case ApiErrorType.unknown:
      default:
        if (httpStatusCode > 0) {
          return "请求失败($httpStatusCode)，请稍后重试";
        }
        return "网络错误，请稍后重试";
    }
  }

  /// 根据HTTP状态码和异常对象分析错误类型
  /// 根据汇编代码 0x88de90 还原
  ApiErrorType _analyzeErrorType(int httpStatusCode, dynamic error) {
    if (httpStatusCode == 401 || httpStatusCode == 403) {
      return ApiErrorType.unauthorized;
    }
    if (httpStatusCode >= 500 && httpStatusCode < 600) {
      return ApiErrorType.serverError;
    }
    if (error != null) {
      final String errorString = error.toString().toLowerCase();
      if (errorString.contains('timeout')) {
        return ApiErrorType.timeout;
      }
      if (errorString.contains('connection') ||
          errorString.contains('socket') ||
          errorString.contains('dns') ||
          errorString.contains('network')) {
        return ApiErrorType.network;
      }
    }
    return ApiErrorType.unknown;
  }

  /// 记录请求指标并保存到文件
  /// 根据汇编代码 0x88e084 还原
  void _logRequestMetrics({
    required String method,
    required String url,
    required DateTime startTime,
    required DateTime endTime,
    required int httpStatusCode,
    dynamic error,
    dynamic responseData,
  }) {
    final int durationMs = endTime.difference(startTime).inMilliseconds;

    final logData = {
      'timestamp': startTime.toIso8601String(),
      'method': method,
      'url': url,
      'duration_ms': durationMs,
      'status_code': httpStatusCode,
      'error': error?.toString(),
    };

    _logBuffer.add(logData);
    if (_logBuffer.length > 100) {
      // 汇编中检查长度是否超过100 (0x64)
      _logBuffer.removeAt(0);
    }

    _saveLogToFile(logData).catchError((e) {
      print("Failed to save log to file: $e");
    });

    // 如果是错误请求，额外使用logger打印到控制台
    if (httpStatusCode != 200 && httpStatusCode != 0 && error != null) {
      _logger.e(
        '请求异常: $method $url',
        error: {
          'statusCode': httpStatusCode,
          'duration': '${durationMs}ms',
          'errorDetails': error.toString(),
          'response': responseData?.toString(),
        },
      );
    }
  }

  /// 将单条日志保存到本地文件
  /// 根据汇编代码 0x88e50c 还原
  Future<void> _saveLogToFile(Map<String, dynamic> logData) async {
    try {
      final String path = await _logFilePath;
      final File file = File(path);
      final String logString =
          "${DateTime.now().toIso8601String()} - ${json.encode(logData)}\n";
      await file.writeAsString(logString, mode: FileMode.append);
    } catch (e, s) {
      _logger.e("保存日志文件失败: $e");
    }
  }

  /// 获取日志文件的完整路径
  /// 根据汇编代码 0x88e6bc 还原
  Future<String> get _logFilePath async {
    final directory = await getApplicationDocumentsDirectory();
    return '${directory.path}/network_logs.txt';
  }
}
