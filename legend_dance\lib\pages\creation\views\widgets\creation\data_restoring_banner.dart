// lib: , url: package:keepdance/pages/creation/views/widgets/creation/data_restoring_banner.dart
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:keepdance/app_theme/app_theme.dart';
import 'package:keepdance/pages/creation/controllers/my_works_controller.dart';

class DataRestoringBanner extends StatelessWidget {
  final MyWorksController? controller;

  const DataRestoringBanner({Key? key, this.controller}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final _controller = controller ?? Get.find<MyWorksController>();
    return Obx(() {
      // 检查是否正在恢复数据
      if (!_controller.isRestoringData.value) {
        return const SizedBox();
      }

      // 如果状态为true，则构建并显示横幅。
      return AnimatedContainer(
        duration: const Duration(milliseconds: 200), // 根据常见的动画时长推断
        curve: Curves.ease, // 推断为常用的Cubic曲线
        padding: EdgeInsets.symmetric(horizontal: 32.w, vertical: 24.h),
        margin: EdgeInsets.symmetric(
          horizontal: AppTheme.horizontalPadding,
          vertical: 16.h,
        ),
        decoration: BoxDecoration(
          // 背景渐变
          gradient: LinearGradient(
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
            tileMode: TileMode.clamp,
            colors: [
              const Color(0xFFFFFFFF).withOpacity(0.1),
              const Color(0xFF000000).withOpacity(0.05),
            ],
          ),
          borderRadius: BorderRadius.circular(24.r),
          // 边框
          border: Border.all(
            color: const Color(0xFFFFFFFF).withOpacity(0.2),
            width: 1.0,
          ),
          // 阴影
          boxShadow: [
            BoxShadow(
              color: const Color(0xFFFFFFFF).withOpacity(0.1),
              offset: const Offset(0.0, 0.0),
              blurRadius: 8.0,
              spreadRadius: 0.0,
              blurStyle: BlurStyle.normal,
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            _buildLoadingIcon(),
            SizedBox(width: 24.w),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Obx(
                    () => Text(
                      _controller.recoveringText.value,
                      style: AppTheme.bodyStyle.copyWith(
                        fontSize: 28.sp,
                        fontWeight: FontWeight.w500,
                        color: const Color(0xFFFAFAFA),
                      ),
                    ),
                  ),
                  SizedBox(height: 12.h),
                  _buildProgressBar(),
                ],
              ),
            ),
            SizedBox(width: 16.w),
            // 进度百分比文本
            Obx(
              () => Text(
                '${_controller.progress.value}%',
                style: AppTheme.captionStyle.copyWith(
                  fontSize: 24.sp,
                  fontWeight: FontWeight.w500,
                  color: const Color(0xFFFAFAFA),
                ),
              ),
            ),
          ],
        ),
      );
    });
  }

  /// 构建加载中的圆形图标
  Widget _buildLoadingIcon() {
    return Container(
      width: 48.w,
      height: 48.w,
      decoration: BoxDecoration(
        color: const Color(0xFFFFFFFF).withOpacity(0.1),
        shape: BoxShape.circle,
      ),
      child: Center(
        child: SizedBox(
          width: 32.w,
          height: 32.w,
          child: CircularProgressIndicator(
            valueColor: const AlwaysStoppedAnimation<Color>(
              const Color(0xFFD9D9D9),
            ),
            strokeWidth: 4.w,
          ),
        ),
      ),
    );
  }

  /// 构建进度条
  Widget _buildProgressBar() {
    final _controller = controller ?? Get.find<MyWorksController>();
    return Container(
      height: 8.h,
      decoration: BoxDecoration(
        color: const Color(0xFFFFFFFF).withOpacity(0.2),
        borderRadius: BorderRadius.circular(4.r),
      ),
      child: Obx(() {
        final progressValue = _controller.progress.value / 100.0;
        return AnimatedContainer(
          duration: const Duration(milliseconds: 100), // 推断一个较短的动画时长
          width: double.infinity, // 占满父容器宽度
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(4.r)),
          child: ClipRRect(
            // 用于裁剪进度条的圆角
            borderRadius: BorderRadius.circular(4.r),
            child: LinearProgressIndicator(
              value: progressValue,
              backgroundColor: Colors.transparent, // 背景由外层Container提供
              valueColor: const AlwaysStoppedAnimation<Color>(
                const Color(0xFFD9D9D9),
              ),
              minHeight: 8.h,
            ),
          ),
        );
      }),
    );
  }
}
