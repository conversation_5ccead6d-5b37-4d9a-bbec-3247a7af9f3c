["D:\\project\\ai-dance\\legend_dance\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\vm_snapshot_data", "D:\\project\\ai-dance\\legend_dance\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\isolate_snapshot_data", "D:\\project\\ai-dance\\legend_dance\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\kernel_blob.bin", "D:\\project\\ai-dance\\legend_dance\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/cupertino_icons/assets/CupertinoIcons.ttf", "D:\\project\\ai-dance\\legend_dance\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/hugeicons/lib/fonts/hugeicons-stroke-rounded.ttf", "D:\\project\\ai-dance\\legend_dance\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/wakelock_plus/assets/no_sleep.js", "D:\\project\\ai-dance\\legend_dance\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\packages/pose_landmarker_plugin/assets/pose_landmarker_lite.task", "D:\\project\\ai-dance\\legend_dance\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "D:\\project\\ai-dance\\legend_dance\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\shaders/ink_sparkle.frag", "D:\\project\\ai-dance\\legend_dance\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.json", "D:\\project\\ai-dance\\legend_dance\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\AssetManifest.bin", "D:\\project\\ai-dance\\legend_dance\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\FontManifest.json", "D:\\project\\ai-dance\\legend_dance\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NOTICES.Z", "D:\\project\\ai-dance\\legend_dance\\build\\app\\intermediates\\flutter\\debug\\flutter_assets\\NativeAssetsManifest.json"]