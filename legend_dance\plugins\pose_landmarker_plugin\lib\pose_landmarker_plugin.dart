library pose_landmarker_plugin;

import 'dart:async';
import 'dart:typed_data';
import 'package:flutter/services.dart';

part 'src/models/pose_landmark.dart';
part 'src/models/pose_detection_result.dart';

/// The main plugin class that provides pose detection functionality.
class PoseLandmarkerPlugin {
  static const String _channelName = 'pose_landmarker_plugin';
  static const MethodChannel _channel = MethodChannel(_channelName);
  
  static final StreamController<List<PoseDetectionResult>> _resultController =
      StreamController<List<PoseDetectionResult>>.broadcast();
  
  static final StreamController<String> _errorController =
      StreamController<String>.broadcast();
  
  static bool _isListening = false;
  
  /// Stream of pose detection results
  static Stream<List<PoseDetectionResult>> get onResult => _resultController.stream;
  
  /// Stream of detection errors
  static Stream<String> get onError => _errorController.stream;
  
  /// Initialize the pose landmarker with optional parameters
  static Future<void> initialize({
    int maxPoses = 1,
    bool forceReinitialize = false,
  }) async {
    _setupMethodChannelListener();
    
    try {
      await _channel.invokeMethod('initialize', {
        'maxPoses': maxPoses,
        'forceReinitialize': forceReinitialize,
      });
    } on PlatformException catch (e) {
      throw PoseLandmarkerException('Failed to initialize: ${e.message}');
    }
  }
  
  /// Detect poses in a YUV420 camera frame
  static Future<void> detect({
    required int width,
    required int height,
    required List<Map<String, dynamic>> planes,
    int? deviceOrientation,
    int? sensorOrientation,
    bool? isFront,
  }) async {
    try {
      await _channel.invokeMethod('detect', {
        'width': width,
        'height': height,
        'planes': planes,
        'deviceOrientation': deviceOrientation,
        'sensorOrientation': sensorOrientation,
        'isFront': isFront,
      });
    } on PlatformException catch (e) {
      throw PoseLandmarkerException('Detection failed: ${e.message}');
    }
  }
  
  /// Detect poses in RGBA image data
  static Future<void> detectRGBA({
    required int width,
    required int height,
    required Uint8List imageData,
    int? sensorOrientation,
    bool? isFront,
  }) async {
    try {
      await _channel.invokeMethod('detectRGBA', {
        'width': width,
        'height': height,
        'imageData': imageData,
        'sensorOrientation': sensorOrientation,
        'isFront': isFront,
      });
    } on PlatformException catch (e) {
      throw PoseLandmarkerException('RGBA detection failed: ${e.message}');
    }
  }
  
  /// Reset the initialization state (useful for troubleshooting)
  static Future<void> resetInitializationState() async {
    try {
      await _channel.invokeMethod('resetInitializationState');
    } on PlatformException catch (e) {
      throw PoseLandmarkerException('Failed to reset state: ${e.message}');
    }
  }
  
  /// Release resources
  static Future<void> release() async {
    try {
      await _channel.invokeMethod('release');
      _isListening = false;
    } on PlatformException catch (e) {
      throw PoseLandmarkerException('Failed to release: ${e.message}');
    }
  }
  
  /// Set up method channel listener for callbacks
  static void _setupMethodChannelListener() {
    if (_isListening) return;
    
    _channel.setMethodCallHandler((call) async {
      switch (call.method) {
        case 'onResult':
          _handleResult(call.arguments);
          break;
        case 'onError':
          _handleError(call.arguments);
          break;
        default:
          print('Unknown method: ${call.method}');
      }
    });
    
    _isListening = true;
  }
  
  /// Handle pose detection results
  static void _handleResult(dynamic arguments) {
    try {
      if (arguments == null || arguments is! List) {
        _resultController.add([]);
        return;
      }
      
      final List<dynamic> resultsList = arguments;
      final List<PoseDetectionResult> results = resultsList
          .map((result) => PoseDetectionResult.fromMap(result))
          .toList();
      
      _resultController.add(results);
    } catch (e) {
      _errorController.add('Error parsing results: $e');
    }
  }
  
  /// Handle detection errors
  static void _handleError(dynamic arguments) {
    try {
      if (arguments is Map<String, dynamic>) {
        final error = arguments['error'] as String?;
        _errorController.add(error ?? 'Unknown error');
      } else {
        _errorController.add('Unknown error format');
      }
    } catch (e) {
      _errorController.add('Error handling error: $e');
    }
  }
  
  /// Dispose of resources
  static void dispose() {
    _resultController.close();
    _errorController.close();
    release();
  }
}

/// Exception thrown by PoseLandmarkerPlugin
class PoseLandmarkerException implements Exception {
  final String message;
  
  const PoseLandmarkerException(this.message);
  
  @override
  String toString() => 'PoseLandmarkerException: $message';
}
