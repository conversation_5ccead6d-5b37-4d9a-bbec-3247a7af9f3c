# Pose Landmarker Plugin

A production-grade Flutter plugin for real-time pose detection using Google MediaPipe Pose Landmarker.

## Features

- ✅ **Real-time pose detection** with 33 pose landmarks per person
- ✅ **Multi-person support** (configurable max poses)
- ✅ **Cross-platform** - Android and iOS support
- ✅ **GPU acceleration** for optimal performance
- ✅ **YUV420 camera frame processing** for live camera feeds
- ✅ **RGBA image processing** for static image analysis
- ✅ **Production-ready** - based on original app implementation

## Requirements

### Android
- **Minimum SDK**: 21 (Android 5.0)
- **Target SDK**: 34
- **MediaPipe Tasks Vision**: 0.10.8

### iOS
- **Minimum Version**: iOS 12.0
- **MediaPipe Tasks Vision**: 0.10.8

## Installation

Add this to your app's `pubspec.yaml`:

```yaml
dependencies:
  pose_landmarker_plugin:
    path: plugins/pose_landmarker_plugin
```

## Usage

### 1. Initialize the Plugin

```dart
import 'package:pose_landmarker_plugin/pose_landmarker_plugin.dart';

// Initialize with default settings (single person detection)
await PoseLandmarkerPlugin.initialize();

// Or configure for multiple people
await PoseLandmarkerPlugin.initialize(maxPoses: 2);
```

### 2. Set up Result Listeners

```dart
// Listen for pose detection results
PoseLandmarkerPlugin.onResult.listen((List<PoseDetectionResult> results) {
  for (final result in results) {
    print('Detected pose with ${result.landmarks.length} landmarks');
    
    // Access specific landmarks
    final nose = result.getLandmark(PoseLandmarkType.nose);
    if (nose != null) {
      print('Nose position: (${nose.x}, ${nose.y})');
    }
    
    // Check detection quality
    print('Average visibility: ${result.averageVisibility}');
    print('Pose valid: ${result.isValid}');
  }
});

// Listen for errors
PoseLandmarkerPlugin.onError.listen((String error) {
  print('Detection error: $error');
});
```

### 3. Process Camera Frames

```dart
// For camera feed (YUV420 format)
await PoseLandmarkerPlugin.detect(
  width: image.width,
  height: image.height,
  planes: [
    {'bytes': image.planes[0].bytes},  // Y plane
    {'bytes': image.planes[1].bytes},  // U plane  
    {'bytes': image.planes[2].bytes},  // V plane
  ],
  deviceOrientation: 0,
  sensorOrientation: 90,
  isFront: true,
);
```

### 4. Process Static Images

```dart
// For RGBA images
await PoseLandmarkerPlugin.detectRGBA(
  width: image.width,
  height: image.height,
  imageData: rgbaBytes,
);
```

### 5. Clean Up

```dart
// Release resources when done
await PoseLandmarkerPlugin.release();

// Or dispose completely
PoseLandmarkerPlugin.dispose();
```

## Advanced Usage

### Pose Landmark Types

The plugin provides 33 pose landmarks following MediaPipe standards:

```dart
// Access specific landmarks
final leftShoulder = result.getLandmark(PoseLandmarkType.leftShoulder);
final rightShoulder = result.getLandmark(PoseLandmarkType.rightShoulder);
final leftElbow = result.getLandmark(PoseLandmarkType.leftElbow);
final rightElbow = result.getLandmark(PoseLandmarkType.rightElbow);

// Calculate pose metrics
final boundingBox = result.boundingBox;
final center = result.center;
```

### Quality Assessment

```dart
// Check landmark confidence
if (landmark.visibility > 0.5 && landmark.presence > 0.5) {
  // High confidence landmark
}

// Overall pose quality
final avgVisibility = result.averageVisibility;
final avgPresence = result.averagePresence;
```

### Error Handling

```dart
try {
  await PoseLandmarkerPlugin.initialize();
} on PoseLandmarkerException catch (e) {
  print('Initialization failed: $e');
}
```

## Performance Tips

1. **Use GPU acceleration** - Enabled by default
2. **Limit max poses** - Only detect as many people as needed
3. **Monitor frame rate** - Process frames at appropriate intervals
4. **Release resources** - Always call `release()` when done

## Integration with Camera

Example with Flutter Camera plugin:

```dart
import 'package:camera/camera.dart';

// In your camera controller setup
controller.startImageStream((CameraImage image) async {
  if (image.format.group == ImageFormatGroup.yuv420) {
    await PoseLandmarkerPlugin.detect(
      width: image.width,
      height: image.height,
      planes: image.planes.map((plane) => {
        'bytes': plane.bytes,
      }).toList(),
      deviceOrientation: deviceOrientation,
      sensorOrientation: controller.description.sensorOrientation,
      isFront: controller.description.lensDirection == CameraLensDirection.front,
    );
  }
});
```

## Model Information

- **Model**: MediaPipe Pose Landmarker Lite
- **File**: `pose_landmarker_lite.task` (2.9MB)
- **Landmarks**: 33 pose keypoints
- **Performance**: Real-time capable on modern devices

## Troubleshooting

### Common Issues

1. **Initialization fails**
   - Ensure model file is included in assets
   - Check minimum SDK/iOS version requirements

2. **No poses detected**
   - Verify camera permissions
   - Check image format (YUV420 or RGBA)
   - Ensure good lighting conditions

3. **Performance issues**
   - Reduce max poses if not needed
   - Lower camera resolution
   - Ensure GPU delegate is working

### Reset Plugin State

```dart
// If having initialization issues
await PoseLandmarkerPlugin.resetInitializationState();
await PoseLandmarkerPlugin.initialize(forceReinitialize: true);
```

## License

This plugin implementation is based on the original production app and uses Google MediaPipe under Apache 2.0 license.