// lib: , url: package:keepdance/pages/creation/views/creation_view.dart

import 'dart:async';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:keepdance/common_widgets/loading_indicator.dart';

import 'package:keepdance/pages/home/<USER>/home_controller.dart';
import 'package:keepdance/controllers/main_controller.dart';
import 'package:keepdance/pages/creation/controllers/my_works_controller.dart';
import 'package:keepdance/models/dance_category.dart';

import './widgets/creation/creation_intro_dialog.dart';
import './widgets/creation/data_restoring_banner.dart';
import './widgets/creation/filter_sort_controls.dart';
import './widgets/creation/floating_action_buttons.dart';
import './widgets/creation/multi_select_bottom_bar.dart';
import './widgets/creation/multi_select_header.dart';
import './widgets/creation/work_item_wrapper.dart';
import './widgets/creation/works_empty_state.dart';
import './widgets/creation/works_loading_state.dart';
import './widgets/creation/works_search_bar.dart';

import 'package:keepdance/utils/analytics/page_analytics.dart';
import 'package:keepdance/utils/device_utils.dart';
import 'package:logger/logger.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:keepdance/app_theme/app_theme.dart';

// 这是一个空的辅助类，可能在原始代码中用于某些目的，但反编译后没有具体内容。
// class id: 1049804
class _ {}

// class id: 1980
class _FilterSortControlsDelegate extends SliverPersistentHeaderDelegate {
  final Function(String) onSortChanged;
  final double extent;

  const _FilterSortControlsDelegate({
    required this.onSortChanged,
    required this.extent,
  });

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    List<BoxShadow>? boxShadow;
    if (!overlapsContent) {
      boxShadow = [
        BoxShadow(
          color: Colors.black.withOpacity(0.08),
          blurRadius: 2.0,
          offset: Offset.zero,
          blurStyle: BlurStyle.normal,
        ),
      ];
    }

    return Container(
      height: extent, // 使用固定的extent高度，而不是shrinkOffset
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: boxShadow,
        shape: BoxShape.rectangle,
      ),
      child: FilterSortControls(),
    );
  }

  @override
  double get minExtent => extent;

  @override
  double get maxExtent => extent;

  @override
  bool shouldRebuild(_FilterSortControlsDelegate oldDelegate) {
    return oldDelegate.onSortChanged != onSortChanged ||
        oldDelegate.extent != extent;
  }
}

// class id: 3634
class _CreationViewState extends State<CreationView> with RestorationMixin {
  static late final Logger _logger = Logger();

  late ScrollController _scrollController;
  Worker? _tabListener;
  String _currentCategory = 'all';
  bool _disposed = false;
  bool _isPageVisibleCallbackScheduled = false;

  @override
  String? get restorationId => widget.restorationId;

  @override
  void restoreState(RestorationBucket? oldBucket, bool initialRestore) {
    // 根据 `UIStateRestorationMixin` 推断，此方法存在
  }

  @override
  void initState() {
    super.initState();
    _disposed = false;
    _initializeControllers();
    _setupPostFrameCallback();
  }

  void _initializeControllers() {
    _scrollController = ScrollController();
    if (Get.isRegistered<MyWorksController>()) {
      final worksController = Get.find<MyWorksController>();
      // ever 监听分类变化
      ever<DanceCategoryType?>(worksController.currentCategory, (category) {
        String categoryString = category?.name ?? 'all';
        if (_currentCategory != categoryString) {
          _logger.d('Controller通知分类切换: $_currentCategory -> $categoryString');
          setState(() {
            _currentCategory = categoryString;
          });
        }
      });
    }
  }

  void _setupPostFrameCallback() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) {
        _logger.d(
          '初始化回调被取消：Widget状态异常 (disposed=$_disposed, mounted=$mounted)',
        );
        return;
      }
      _logger.i('创作页面已加载');
      if (Get.isRegistered<MyWorksController>()) {
        final controller = Get.find<MyWorksController>();
        controller.refreshData();
        controller.refreshVipStatus();
        _logger.d('创作页面初始化时已主动刷新VIP状态');
      }

      // A->B跳转返回后，页面可能不会触发 onResume，需要手动修复
      Future.delayed(const Duration(milliseconds: 200), () {
        if (!mounted) {
          _logger.d(
            'A→B跳转修复回调被取消：Widget状态异常 (disposed=$_disposed, mounted=$mounted)',
          );
          return;
        }
        Future.delayed(const Duration(milliseconds: 500), () async {
          if (!mounted) return;
          if (Get.isRegistered<MyWorksController>()) {
            final controller = Get.find<MyWorksController>();
            await controller.refreshVipStatus();
            _logger.d(
              'A→B跳转修复：延迟刷新VIP状态完成，canDeleteWorks=${controller.canDeleteWorks.value}',
            );
          }
        });
      });
    });
  }

  @override
  void dispose() {
    _disposed = true;
    _tabListener?.dispose();
    _tabListener = null;
    _scrollController.dispose();
    _logger.d("CreationView已完全清理所有资源");
    super.dispose();
  }

  bool _isItemVisible(int index) {
    if (index < 100) {
      return true;
    }
    if (!_scrollController.hasClients ||
        _scrollController.position.extentAfter == 0) {
      return true;
    }

    try {
      if (index < 200) {
        final RenderBox renderBox = context.findRenderObject() as RenderBox;
        final position = renderBox.localToGlobal(Offset.zero);
        final viewportHeight = renderBox.size.height;

        final scrollPixels = _scrollController.position.pixels;
        final visiblePage = (scrollPixels / (viewportHeight * 2))
            .clamp(0.0, 200.0)
            .toInt();

        return index < (visiblePage + 200);
      } else {
        final RenderBox renderBox = context.findRenderObject() as RenderBox;
        final itemHeight = 212.0.h; // 假设item高度
        final itemsPerRow = 2;
        final rowHeight = itemHeight + 16.h; // 假设行间距

        final scrollPixels = _scrollController.position.pixels;
        final viewportHeight = renderBox.size.height;

        final itemTop = (index ~/ itemsPerRow) * rowHeight;
        final itemBottom = itemTop + itemHeight;

        final visibleTop = scrollPixels;
        final visibleBottom = scrollPixels + viewportHeight;

        return itemBottom > visibleTop && itemTop < visibleBottom;
      }
    } catch (e) {
      _logger.w("大列表可见性检测失败: $e");
      return false;
    }
  }

  Future<void> _onPageVisible() async {
    if (_disposed) {
      _logger.w("⚠️ _onPageVisible被调用但Widget已disposed，忽略操作");
      return;
    }
    if (!mounted) {
      _logger.w("⚠️ _onPageVisible被调用但Widget未mounted，忽略操作");
      return;
    }

    try {
      _logger.i("创作页面变为可见");
      if (Get.isRegistered<MyWorksController>()) {
        final worksController = Get.find<MyWorksController>();
        await worksController.refreshVipStatus();
        _logger.d("页面可见时已刷新VIP状态");

        _logger.d(
          "检查创作介绍弹窗状态: shouldShow=${worksController.shouldShowCreationIntroDialog.value}",
        );
        await _checkAndShowCreationIntro(worksController);
      } else {
        _logger.w("MyWorksController未注册，无法检查创作介绍弹窗");
      }
    } catch (e) {
      _logger.e("_onPageVisible执行异常: $e");
    }
  }

  Future<void> _checkAndShowCreationIntro(MyWorksController controller) async {
    // 延迟以确保UI稳定
    Future.delayed(const Duration(milliseconds: 200), () {
      if (_disposed) {
        _logger.d("创作介绍弹窗检查被取消：Widget已disposed");
        return;
      }
      if (!mounted) {
        _logger.d("创作介绍弹窗检查被取消：Widget未mounted");
        return;
      }

      if (controller.shouldShowCreationIntroDialog.value) {
        _showCreationIntroDialog(controller);
      }
    });
  }

  Future<void> _showCreationIntroDialog(MyWorksController controller) async {
    _logger.i("显示创作介绍弹窗");
    if (context.mounted) {
      await showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext dialogContext) {
          return const CreationIntroDialog();
        },
      );
      await controller.markCreationIntroShown();
    }
  }

  Widget _buildMainContentSliver(BuildContext context) {
    final worksController = Get.find<MyWorksController>();
    return Obx(() {
      if (worksController.isLoading.value) {
        return SliverFillRemaining(
          hasScrollBody: true,
          child: WorksLoadingState(),
        );
      }

      if (worksController.works.isEmpty) {
        return SliverFillRemaining(
          hasScrollBody: true,
          child: WorksEmptyState(),
        );
      }

      return SliverMainAxisGroup(
        slivers: [
          SliverPadding(
            padding: EdgeInsets.only(
              left: AppTheme.horizontalPadding.w,
              right: AppTheme.horizontalPadding.w,
              top: 16.h,
              bottom: 16.h,
            ),
            sliver: SliverGrid(
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 16.w,
                mainAxisSpacing: AppTheme.itemSpacing,
                childAspectRatio: 0.8,
              ),
              delegate: SliverChildBuilderDelegate(
                (BuildContext context, int index) {
                  final item = worksController.works[index];
                  // 异步检查预加载
                  Future.microtask(
                    () => worksController.checkPreloadNextPage(index),
                  );

                  return RepaintBoundary(
                    key: ValueKey('work_item_${item.id}'),
                    child: WorkItemWrapper(
                      workItemData: item,
                      controller: worksController,
                    ),
                  );
                },
                childCount: worksController.works.length,
                addAutomaticKeepAlives: true,
                addRepaintBoundaries: true,
              ),
            ),
          ),
          SliverToBoxAdapter(
            child: Obx(() {
              if (worksController.works.isNotEmpty &&
                  worksController.isFetchingMore.value) {
                return Padding(
                  padding: EdgeInsets.only(top: 16.h, bottom: 32.h),
                  child: Center(
                    child: CircularProgressIndicator(
                      strokeWidth: 2.0,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Colors.white.withOpacity(0.8),
                      ),
                    ),
                  ),
                );
              }
              return const SizedBox.shrink();
            }),
          ),
        ],
      );
    });
  }

  Widget _buildSmartScrollContent(BuildContext context) {
    final worksController = Get.find<MyWorksController>();
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      behavior: HitTestBehavior.translucent,
      child: Focus(
        onFocusChange: (hasFocus) {
          if (!hasFocus) {
            // 失去焦点时刷新数据
            if (Get.isRegistered<MyWorksController>()) {
              final controller = Get.find<MyWorksController>();
              if (!controller.isSearching.value) {
                Future.microtask(() async {
                  await controller.refreshData();
                  await controller.refreshVipStatus();
                  _logger.d("页面重新获得焦点，已强制刷新VIP状态");
                });
              } else {
                Future.microtask(() async {
                  await controller.refreshVipStatus();
                  _logger.d("搜索状态下页面获得焦点，仅刷新VIP状态");
                });
              }
            }
          }
        },
        child: Obx(() {
          List<Widget> slivers = [];

          // 多选头部
          if (worksController.isMultiSelectMode.value) {
            slivers.add(
              SliverToBoxAdapter(
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 250),
                  height: 100.h,
                  child: MultiSelectHeader(),
                ),
              ),
            );
          }

          // 搜索栏
          if (worksController.isMultiSelectMode.value ||
              worksController.shouldShowSearchBar) {
            slivers.add(
              SliverToBoxAdapter(
                child: Container(
                  padding: EdgeInsets.only(top: 16.h),
                  child: WorksSearchBar(),
                ),
              ),
            );
          }

          // 数据恢复横幅
          slivers.add(
            SliverToBoxAdapter(
              child: DataRestoringBanner(controller: worksController),
            ),
          );

          // 筛选和排序控件
          slivers.add(
            SliverPersistentHeader(
              delegate: _FilterSortControlsDelegate(
                onSortChanged: (v) {},
                extent: 104.h,
              ),
              pinned: true,
            ),
          );

          // 主要内容区域
          slivers.add(_buildMainContentSliver(context));

          return Stack(
            alignment: AlignmentDirectional.bottomCenter,
            fit: StackFit.expand,
            children: [
              CustomScrollView(
                key: PageStorageKey<String>(
                  'creation_scroll_${_currentCategory}',
                ),
                controller: _scrollController,
                physics: const BouncingScrollPhysics(),
                slivers: slivers,
              ),
              // 多选底部栏
              if (worksController.isMultiSelectMode.value)
                Positioned(
                  left: 0,
                  right: 0,
                  bottom: 0,
                  child: MultiSelectBottomBar(controller: worksController),
                ),
              // 悬浮按钮
              FloatingActionButtons(controller: worksController),
            ],
          );
        }),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (!_isPageVisibleCallbackScheduled) {
      final homeController = Get.find<HomeController>();
      final mainController = Get.find<MainController>();
      _tabListener = ever<int>(mainController.tabChanges, (index) {
        _logger.d("页面切换到索引: $index");
        if (index == homeController.creationTabIndex) {
          if (mounted) {
            _logger.i("切换到创作页面，准备检查创作介绍弹窗和刷新VIP状态");
            if (Get.isRegistered<MyWorksController>()) {
              Get.find<MyWorksController>().refreshVipStatus();
              _logger.d("已触发VIP状态刷新");
            }
            Future.delayed(const Duration(milliseconds: 250), () {
              if (mounted) {
                _onPageVisible();
              } else {
                _logger.d(
                  '延迟回调被取消：Widget状态异常 (disposed=$_disposed, mounted=$mounted)',
                );
              }
            });
          }
        }
      });
      _isPageVisibleCallbackScheduled = true;
    }

    // 页面浏览分析
    PackageInfo.fromPlatform().then((packageInfo) {
      final homeController = Get.find<HomeController>();
      bool? isVip = homeController.userInfo.value?.isVip;
      String previousRoute = Get.previousRoute;
      PageAnalytics.trackAIWorkshopPageView(
        appVersion: packageInfo.version,
        currentTab: homeController.tabIndex.value,
        isLoggedIn: homeController.userInfo.value != null,
        isVip: isVip ?? false,
        sourcePage: previousRoute,
        userId: homeController.userInfo.value?.id?.toString(),
      );
    });

    final mediaQueryData = MediaQuery.of(context);

    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        children: [
          Container(
            height:
                mediaQueryData.padding.top +
                (DeviceUtils.isLargeScreen() ? 186.h : 56.0),
            padding: EdgeInsets.only(left: 32.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                SizedBox(width: 80.w),
                Text(
                  '我的作品',
                  style: AppTheme.headlineStyle.copyWith(
                    fontSize: 36.sp,
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                    shadows: [
                      Shadow(
                        color: Colors.black.withOpacity(0.3),
                        offset: const Offset(0, 2),
                        blurRadius: 8.0,
                      ),
                    ],
                  ),
                ),
                Material(
                  type: MaterialType.transparency,
                  child: InkWell(
                    onTap: () {
                      _logger.d("点击统计图标，准备跳转到统计页面");
                      Get.toNamed('/works-statistics');
                    },
                    customBorder: const CircleBorder(),
                    child: Container(
                      width: 80.w,
                      height: 80.w,
                      alignment: Alignment.center,
                      child: HugeIcon(
                        icon: HugeIcons.strokeRoundedAdd01,
                        size: 48.sp,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: SafeArea(
              top: false,
              bottom: true,
              child: Container(
                decoration: const BoxDecoration(color: Colors.white),
                child: _buildSmartScrollContent(context),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// class id: 4269
class CreationView extends StatefulWidget {
  final String? restorationId;
  const CreationView({Key? key, this.restorationId}) : super(key: key);

  @override
  State<CreationView> createState() => _CreationViewState();
}
