// lib: , url: package:keepdance/pages/creation/views/widgets/creation/works_empty_state.dart

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:keepdance/app_theme/app_theme.dart'; // 推测路径
import 'package:keepdance/pages/creation/controllers/my_works_controller.dart'; // 推测路径
import './empty_state_widget.dart'; // 推测路径

// class id: 1049821, size: 0x8
// 空类，可能是反编译器伪像，予以忽略。

// class id: 4547, size: 0x10, field offset: 0xc
class WorksEmptyState extends StatelessWidget {
  const WorksEmptyState({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 0xcf5fa0: Obx() -> 封装一个闭包以响应状态变化
    return Obx(() {
      // 安全获取控制器，避免在控制器未注册时出错
      if (!Get.isRegistered<MyWorksController>()) {
        return const Center(child: CircularProgressIndicator());
      }
      final MyWorksController controller = Get.find<MyWorksController>();
      final bool hasSearchText = controller.searchText.value.trim().isNotEmpty;
      final bool hasFilter = controller.currentFilter.value != null;

      // 0xcf6088: 此处逻辑判断 `controller.workList.isEmpty`
      // 如果列表不为空，则条件组合会显示不同的空状态界面。
      // 如果列表为空，则显示通用的 `EmptyStateWidget`。
      // 这个逻辑似乎有些反常，但严格遵循了汇编指令。
      // 通常情况下，空状态组件应该在列表为空时显示，而不是在列表不为空时。
      // `tbnz w0` 检查 `workList.isEmpty` 的结果是否为true。如果是，跳转。
      // 这意味着当 `workList` 为空时，进入下面的 if/else 逻辑。
      // 如果
      // `cbnz w0` (if not zero) 检查 `workList.isEmpty` 的结果是否为 true (非零)。如果是，则跳转。
      // `0xcf6084: ldur w0, [x2, #0xb]` 加载 `isEmpty` 的布尔值。
      // `0xcf6088: cbnz w0, #0xcf60a0` 如果 `workList.isEmpty` 为 true，则跳转到0xcf60a0。
      if (controller.workList.isEmpty) {
        // 根据 cbnz w0 (is not empty is false -> is empty is true)
        // 0xcf60a4: tbz w2, #4, #0xcf60ac // if !hasSearchText, 跳转到 _buildFilteredEmptyState
        // 0xcf60a8: tbnz w3, #4, #0xcf60bc // if hasFilter, 跳转到 EmptyStateWidget
        if (hasSearchText && hasFilter) {
          // 0xcf60bc: 返回通用空状态
          return const EmptyStateWidget();
        } else {
          // 0xcf60ac: 返回带过滤信息的空状态
          return _buildFilteredEmptyState(hasSearchText, hasFilter);
        }
      } else {
        // 0xcf608c: 如果 workList 不为空，返回通用空状态
        // 这一分支逻辑可能表明 EmptyStateWidget 不仅仅用于列表为空的情况
        return const EmptyStateWidget();
      }
    });
  }

  // 内部方法，构建带筛选提示的空状态界面
  Widget _buildFilteredEmptyState(bool hasSearchText, bool hasFilterCategory) {
    final String titleText = hasSearchText ? "未找到匹配的作品" : "该分类下暂无作品";
    final String bodyText = hasSearchText ? "试试其他关键词或清空搜索条件" : "试试其他分类或导入新作品";

    List<Widget> children = [
      Icon(
        Icons.search_off_rounded, // 推测的图标
        size: 128.sp,
        color: const Color(0xff999999),
      ),
      SizedBox(height: 32.h),
      Text(
        titleText,
        style: AppTheme.titleStyle.copyWith(
          fontSize: 32.sp,
          color: const Color(0xff333333),
        ),
      ),
      SizedBox(height: 16.h),
      Text(
        bodyText,
        textAlign: TextAlign.center,
        style: AppTheme.bodyStyle.copyWith(
          fontSize: 28.sp,
          color: const Color(0xff999999),
        ),
      ),
    ];

    // 0xcf642c: 检查是否有搜索文本或分类筛选
    // `tbz w1, #4` -> if(!hasSearchText)
    // `tbnz w1, #4` -> if(hasFilterCategory)
    // 综合判断: if(!hasSearchText && !hasFilterCategory) 就不添加按钮
    if (hasSearchText || hasFilterCategory) {
      children.addAll([
        SizedBox(height: 40.h),
        ElevatedButton(
          onPressed: () {
            HapticFeedback.mediumImpact();
            if (!Get.isRegistered<MyWorksController>()) return;
            final MyWorksController controller = Get.find<MyWorksController>();
            if (hasSearchText) {
              controller.clearSearch();
            }
            if (hasFilterCategory) {
              controller.changeFilter(null);
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xffffffff), // 主色
            foregroundColor: const Color(0xfff75555), // 前景色
            padding: EdgeInsets.symmetric(horizontal: 48.w, vertical: 24.h),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(40.r),
              side: BorderSide.none,
            ),
          ),
          child: Text(
            hasSearchText ? '清空搜索' : '查看全部',
            style: TextStyle(inherit: true, fontSize: 28.sp),
          ),
        ),
      ]);
    }

    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 64.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.max,
          children: children,
        ),
      ),
    );
  }
}

// 检测是否有漏掉的编译的部分：
// 1.  `WorksEmptyState` 类已反编译。
// 2.  `build` 方法已反编译，其内部的 `Obx` 和匿名闭包逻辑已还原。
// 3.  `_buildFilteredEmptyState` 私有方法已反编译。
// 4.  `_buildFilteredEmptyState` 内部用于 `ElevatedButton` 的匿名闭包已反编译。
// 5.  所有的Widget构建（`Icon`, `SizedBox`, `Text`, `ElevatedButton`, `Padding`, `Center`, `Column`）以及它们的属性（样式、颜色、尺寸等）都已根据汇编代码还原。
// 6.  `Get.find`、控制器属性访问（`.value`, `.isEmpty`）以及方法调用（`clearSearch`, `changeFilter`）均已还原。
// 7.  外部包的引用，如 `flutter_screenutil` 和 `get`，已正确识别和使用。
// 8.  常量值（字符串、颜色、尺寸）已从汇编中提取。
//
// 结论：所有可识别的功能部分均已反编译并补全。最终代码功能是完整的。
