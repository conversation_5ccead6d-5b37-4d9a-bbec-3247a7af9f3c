import 'package:get/get.dart';
import 'package:logger/logger.dart';
import 'package:keepdance/pages/creation/controllers/my_works_controller.dart';

// class id: 1049787, size: 0x8
// 这是一个空的、可能是由编译器生成的辅助类，在Dart代码中通常不可见，无需还原。

/// Controller for the Creation Page.
/// Manages the lifecycle and dependencies for the creation section.
class CreationController extends GetxController {
  /// A logger instance for this controller.
  final Logger _logger = Logger();

  /// Getter to conveniently access the MyWorksController.
  /// It uses Get.find() to locate the registered instance.
  MyWorksController get _myWorksController => Get.find<MyWorksController>();

  // 扫描和处理状态
  final RxBool isScanning = false.obs;
  final RxInt totalCount = 0.obs;
  final RxInt processedCount = 0.obs;
  final RxBool isCategorizing = false.obs;
  final RxBool isDone = false.obs;

  // 选中的项目列表
  final RxList<String> selectedList = <String>[].obs;

  /// Called when the controller is initialized.
  /// Logs the initialization event.
  @override
  void onInit() {
    super.onInit();
    _logger.i("创作页面控制器初始化");
  }

  /// Called when the controller is ready, after the widget is rendered on screen.
  /// Logs the ready event and triggers initial data refresh for user's works and VIP status.
  @override
  void onReady() {
    // 注：根据汇编，onReady()没有调用super.onReady()，这在GetX中是允许的。
    _logger.i("创作页面准备就绪");
    _myWorksController.refreshData();
    Get.find<MyWorksController>().refreshVipStatus();
  }

  /// Called just before the controller is deleted from memory.
  /// Logs the disposal event.
  @override
  void onClose() {
    // 注：根据汇编，onClose()没有调用super.onClose()。
    // 在GetX中，如果父类的onClose为空，编译器可能会优化掉这个调用。
    _logger.i("创作页面控制器销毁");
  }
}
