import Flutter
import UIKit
import MediaPipeTasksVision
import AVFoundation

public class PoseLandmarkerPlugin: NSObject, FlutterPlugin {
    private static let channelName = "pose_landmarker_plugin"
    private var channel: FlutterMethodChannel?
    private var poseLandmarker: PoseLandmarker?
    private var isInitialized = false
    
    public static func register(with registrar: FlutterPluginRegistrar) {
        let channel = FlutterMethodChannel(name: channelName, binaryMessenger: registrar.messenger())
        let instance = PoseLandmarkerPlugin()
        instance.channel = channel
        registrar.addMethodCallDelegate(instance, channel: channel)
    }
    
    public func handle(_ call: FlutterMethodCall, result: @escaping FlutterResult) {
        print("PoseLandmarkerPlugin: Method called: \(call.method)")
        
        switch call.method {
        case "initialize":
            initialize(call: call, result: result)
        case "detect":
            detect(call: call, result: result)
        case "detectRGBA":
            detectRGBA(call: call, result: result)
        case "resetInitializationState":
            resetInitializationState(result: result)
        case "release":
            release(result: result)
        default:
            result(FlutterMethodNotImplemented)
        }
    }
    
    private func initialize(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard let args = call.arguments as? [String: Any] else {
            result(FlutterError(code: "INVALID_ARGS", message: "Invalid arguments", details: nil))
            return
        }
        
        let maxPoses = args["maxPoses"] as? Int ?? 1
        let forceReinitialize = args["forceReinitialize"] as? Bool ?? false
        
        if isInitialized && !forceReinitialize {
            print("PoseLandmarkerPlugin: Already initialized")
            result(nil)
            return
        }
        
        // Release existing instance if reinitializing
        poseLandmarker = nil
        
        do {
            // Get model path from bundle
            guard let modelPath = Bundle.main.path(forResource: "pose_landmarker_lite", ofType: "task") else {
                result(FlutterError(code: "MODEL_ERROR", message: "Model file not found in bundle", details: nil))
                return
            }
            
            // Create base options
            let baseOptions = BaseOptions()
            baseOptions.modelAssetPath = modelPath
            baseOptions.delegate = .GPU // Use GPU if available
            
            // Create pose landmarker options
            let options = PoseLandmarkerOptions()
            options.baseOptions = baseOptions
            options.runningMode = .liveStream
            options.numPoses = maxPoses
            options.poseLandmarkerLiveStreamDelegate = self
            
            // Create pose landmarker
            poseLandmarker = try PoseLandmarker(options: options)
            isInitialized = true
            
            print("PoseLandmarkerPlugin: Initialized successfully with maxPoses: \(maxPoses)")
            result(nil)
            
        } catch {
            print("PoseLandmarkerPlugin: Failed to initialize: \(error)")
            result(FlutterError(code: "INIT_ERROR", message: "Failed to initialize: \(error.localizedDescription)", details: nil))
        }
    }
    
    private func detect(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard isInitialized, let poseLandmarker = poseLandmarker else {
            result(FlutterError(code: "NOT_INITIALIZED", message: "PoseLandmarker not initialized", details: nil))
            return
        }
        
        guard let args = call.arguments as? [String: Any],
              let width = args["width"] as? Int,
              let height = args["height"] as? Int,
              let planes = args["planes"] as? [[String: Any]] else {
            result(FlutterError(code: "INVALID_ARGS", message: "Missing required arguments for detect", details: nil))
            return
        }
        
        // Convert YUV420 to UIImage
        guard let image = convertYUV420ToUIImage(planes: planes, width: width, height: height) else {
            result(FlutterError(code: "CONVERSION_ERROR", message: "Failed to convert YUV to UIImage", details: nil))
            return
        }
        
        // Create MPImage
        do {
            let mpImage = try MPImage(uiImage: image)
            let timestamp = Int(Date().timeIntervalSince1970 * 1000)
            
            // Async detection
            try poseLandmarker.detectAsync(image: mpImage, timestampInMilliseconds: timestamp)
            
            // Result will be handled by delegate
            result(nil)
            
        } catch {
            print("PoseLandmarkerPlugin: Detection error: \(error)")
            result(FlutterError(code: "DETECT_ERROR", message: "Detection failed: \(error.localizedDescription)", details: nil))
        }
    }
    
    private func detectRGBA(call: FlutterMethodCall, result: @escaping FlutterResult) {
        guard isInitialized, let poseLandmarker = poseLandmarker else {
            result(FlutterError(code: "NOT_INITIALIZED", message: "PoseLandmarker not initialized", details: nil))
            return
        }
        
        guard let args = call.arguments as? [String: Any],
              let width = args["width"] as? Int,
              let height = args["height"] as? Int,
              let imageData = args["imageData"] as? FlutterStandardTypedData else {
            result(FlutterError(code: "INVALID_ARGS", message: "Missing required arguments for detectRGBA", details: nil))
            return
        }
        
        // Convert RGBA data to UIImage
        guard let image = convertRGBAToUIImage(data: imageData.data, width: width, height: height) else {
            result(FlutterError(code: "CONVERSION_ERROR", message: "Failed to convert RGBA to UIImage", details: nil))
            return
        }
        
        // Create MPImage and detect
        do {
            let mpImage = try MPImage(uiImage: image)
            let timestamp = Int(Date().timeIntervalSince1970 * 1000)
            
            // Async detection
            try poseLandmarker.detectAsync(image: mpImage, timestampInMilliseconds: timestamp)
            
            // Result will be handled by delegate
            result(nil)
            
        } catch {
            print("PoseLandmarkerPlugin: RGBA detection error: \(error)")
            result(FlutterError(code: "DETECT_ERROR", message: "RGBA detection failed: \(error.localizedDescription)", details: nil))
        }
    }
    
    private func resetInitializationState(result: @escaping FlutterResult) {
        isInitialized = false
        print("PoseLandmarkerPlugin: Initialization state reset")
        result(nil)
    }
    
    private func release(result: @escaping FlutterResult) {
        poseLandmarker = nil
        isInitialized = false
        print("PoseLandmarkerPlugin: Released")
        result(nil)
    }
    
    // MARK: - Helper Methods
    
    private func convertYUV420ToUIImage(planes: [[String: Any]], width: Int, height: Int) -> UIImage? {
        // Simplified YUV to RGB conversion for iOS
        // In a real implementation, you would need proper YUV420 to RGB conversion
        // This is a placeholder - you may need to use CoreVideo or similar
        
        guard planes.count >= 3,
              let yData = planes[0]["bytes"] as? FlutterStandardTypedData,
              let uData = planes[1]["bytes"] as? FlutterStandardTypedData,
              let vData = planes[2]["bytes"] as? FlutterStandardTypedData else {
            return nil
        }
        
        // Create a basic grayscale image from Y plane for now
        let yBytes = yData.data
        let bytesPerPixel = 4
        let bytesPerRow = width * bytesPerPixel
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        
        guard let context = CGContext(
            data: nil,
            width: width,
            height: height,
            bitsPerComponent: 8,
            bytesPerRow: bytesPerRow,
            space: colorSpace,
            bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue
        ) else {
            return nil
        }
        
        let pixelData = context.data!.bindMemory(to: UInt8.self, capacity: width * height * bytesPerPixel)
        
        // Simple Y to RGB conversion (grayscale)
        for i in 0..<(width * height) {
            let y = yBytes[i]
            let offset = i * bytesPerPixel
            pixelData[offset] = y     // R
            pixelData[offset + 1] = y // G  
            pixelData[offset + 2] = y // B
            pixelData[offset + 3] = 255 // A
        }
        
        guard let cgImage = context.makeImage() else {
            return nil
        }
        
        return UIImage(cgImage: cgImage)
    }
    
    private func convertRGBAToUIImage(data: Data, width: Int, height: Int) -> UIImage? {
        let bytesPerPixel = 4
        let bytesPerRow = width * bytesPerPixel
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        
        guard let context = CGContext(
            data: nil,
            width: width,
            height: height,
            bitsPerComponent: 8,
            bytesPerRow: bytesPerRow,
            space: colorSpace,
            bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue
        ) else {
            return nil
        }
        
        let pixelData = context.data!.bindMemory(to: UInt8.self, capacity: width * height * bytesPerPixel)
        data.copyBytes(to: pixelData, count: data.count)
        
        guard let cgImage = context.makeImage() else {
            return nil
        }
        
        return UIImage(cgImage: cgImage)
    }
}

// MARK: - PoseLandmarkerLiveStreamDelegate

extension PoseLandmarkerPlugin: PoseLandmarkerLiveStreamDelegate {
    public func poseLandmarker(_ poseLandmarker: PoseLandmarker, 
                               didFinishDetection result: PoseLandmarkerResult?, 
                               timestampInMilliseconds: Int, 
                               error: Error?) {
        
        DispatchQueue.main.async { [weak self] in
            guard let self = self, let channel = self.channel else { return }
            
            if let error = error {
                print("PoseLandmarkerPlugin: MediaPipe error: \(error)")
                let errorData: [String: Any] = ["error": error.localizedDescription]
                channel.invokeMethod("onError", arguments: errorData)
                return
            }
            
            guard let result = result else {
                // No pose detected
                channel.invokeMethod("onResult", arguments: [])
                return
            }
            
            do {
                var landmarksData: [[String: Any]] = []
                
                for (index, landmarks) in result.landmarks.enumerated() {
                    var poseLandmarks: [[String: Any]] = []
                    
                    for landmark in landmarks {
                        let point: [String: Any] = [
                            "x": landmark.x,
                            "y": landmark.y,
                            "z": landmark.z,
                            "visibility": landmark.visibility?.floatValue ?? 1.0,
                            "presence": landmark.presence?.floatValue ?? 1.0
                        ]
                        poseLandmarks.append(point)
                    }
                    
                    let poseData: [String: Any] = ["landmarks": poseLandmarks]
                    landmarksData.append(poseData)
                }
                
                print("PoseLandmarkerPlugin: Pose detection successful, poses found: \(landmarksData.count)")
                channel.invokeMethod("onResult", arguments: landmarksData)
                
            } catch {
                print("PoseLandmarkerPlugin: Error processing pose results: \(error)")
                let errorData: [String: Any] = ["error": error.localizedDescription]
                channel.invokeMethod("onError", arguments: errorData)
            }
        }
    }
}